"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const encryption_1 = require("../src/utils/encryption");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seed...');
    const hashedPassword = await (0, encryption_1.hashPassword)('Password123!');
    const user1 = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'alice',
            passwordHash: hashedPassword,
            firstName: 'Alice',
            lastName: '<PERSON>',
            status: 'online',
            isVerified: true,
        },
    });
    const user2 = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'bob',
            passwordHash: hashedPassword,
            firstName: '<PERSON>',
            lastName: '<PERSON>',
            status: 'online',
            isVerified: true,
        },
    });
    const user3 = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'charlie',
            passwordHash: hashedPassword,
            firstName: 'Charlie',
            lastName: 'Brown',
            status: 'away',
            isVerified: true,
        },
    });
    console.log('✅ Created demo users');
    const directRoom = await prisma.chatRoom.create({
        data: {
            type: 'direct',
            isPrivate: true,
            createdBy: user1.id,
            members: {
                create: [
                    {
                        userId: user1.id,
                        role: 'member',
                    },
                    {
                        userId: user2.id,
                        role: 'member',
                    },
                ],
            },
        },
    });
    const groupRoom = await prisma.chatRoom.create({
        data: {
            name: 'General Discussion',
            description: 'A place for general conversations',
            type: 'group',
            isPrivate: false,
            createdBy: user1.id,
            members: {
                create: [
                    {
                        userId: user1.id,
                        role: 'admin',
                    },
                    {
                        userId: user2.id,
                        role: 'member',
                    },
                    {
                        userId: user3.id,
                        role: 'member',
                    },
                ],
            },
        },
    });
    console.log('✅ Created demo chat rooms');
    const message1 = await prisma.message.create({
        data: {
            roomId: directRoom.id,
            senderId: user1.id,
            content: 'Hey Bob! How are you doing?',
            messageType: 'text',
        },
    });
    const message2 = await prisma.message.create({
        data: {
            roomId: directRoom.id,
            senderId: user2.id,
            content: 'Hi Alice! I\'m doing great, thanks for asking!',
            messageType: 'text',
        },
    });
    const message3 = await prisma.message.create({
        data: {
            roomId: groupRoom.id,
            senderId: user1.id,
            content: 'Welcome to the general discussion room everyone!',
            messageType: 'text',
        },
    });
    const message4 = await prisma.message.create({
        data: {
            roomId: groupRoom.id,
            senderId: user3.id,
            content: 'Thanks Alice! Excited to be here.',
            messageType: 'text',
            replyTo: message3.id,
        },
    });
    console.log('✅ Created demo messages');
    await prisma.messageReaction.create({
        data: {
            messageId: message1.id,
            userId: user2.id,
            emoji: '👋',
        },
    });
    await prisma.messageReaction.create({
        data: {
            messageId: message3.id,
            userId: user2.id,
            emoji: '🎉',
        },
    });
    console.log('✅ Created demo reactions');
    console.log('🎉 Database seed completed successfully!');
    console.log('\nDemo users created:');
    console.log('- <EMAIL> (password: Password123!)');
    console.log('- <EMAIL> (password: Password123!)');
    console.log('- <EMAIL> (password: Password123!)');
}
main()
    .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map