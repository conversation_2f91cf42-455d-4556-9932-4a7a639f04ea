import { createClient } from 'redis';
import { env, isDevelopment } from './environment';
import { logger } from '@/utils/logger';

let redisClient: ReturnType<typeof createClient> | null = null;

export const createRedisClient = async () => {
  if (!env.REDIS_URL) {
    logger.warn('Redis URL not provided, skipping Redis connection');
    return null;
  }

  try {
    const client = createClient({
      url: env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500),
      },
    });

    client.on('error', (err) => {
      logger.error('Redis Client Error:', err);
    });

    client.on('connect', () => {
      logger.info('Redis client connected');
    });

    client.on('ready', () => {
      logger.info('Redis client ready');
    });

    client.on('end', () => {
      logger.info('Redis client disconnected');
    });

    await client.connect();
    redisClient = client;
    
    return client;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    return null;
  }
};

export const getRedisClient = () => redisClient;

export const disconnectRedis = async () => {
  if (redisClient) {
    await redisClient.disconnect();
    redisClient = null;
  }
};

// Session storage helpers
export const setSession = async (key: string, value: any, ttl: number = 3600) => {
  if (!redisClient) return false;
  
  try {
    await redisClient.setEx(key, ttl, JSON.stringify(value));
    return true;
  } catch (error) {
    logger.error('Failed to set session:', error);
    return false;
  }
};

export const getSession = async (key: string) => {
  if (!redisClient) return null;
  
  try {
    const value = await redisClient.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error('Failed to get session:', error);
    return null;
  }
};

export const deleteSession = async (key: string) => {
  if (!redisClient) return false;
  
  try {
    await redisClient.del(key);
    return true;
  } catch (error) {
    logger.error('Failed to delete session:', error);
    return false;
  }
};
