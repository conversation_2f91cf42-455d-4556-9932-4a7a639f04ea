import { Request, Response, NextFunction } from 'express';
import { AuthService } from '@/services/authService';
import { verifyRefreshToken } from '@/utils/jwt';
import { AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import type { RegisterInput, LoginInput, UpdateProfileInput } from '../../../shared/validation/auth';

const authService = new AuthService();

export class AuthController {
  async register(req: Request, res: Response, next: NextFunction) {
    try {
      const data: RegisterInput = req.body;
      const result = await authService.register(data);
      
      res.status(201).json({
        message: 'User registered successfully',
        ...result,
      });
    } catch (error) {
      next(error);
    }
  }

  async login(req: Request, res: Response, next: NextFunction) {
    try {
      const data: LoginInput = req.body;
      const result = await authService.login(data);
      
      res.status(200).json({
        message: 'Login successful',
        ...result,
      });
    } catch (error) {
      next(error);
    }
  }

  async logout(req: Request, res: Response, next: NextFunction) {
    try {
      const user = (req as AuthenticatedRequest).user;
      await authService.logout(user.id);

      res.status(200).json({
        message: 'Logout successful',
      });
    } catch (error) {
      next(error);
    }
  }

  async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        res.status(400).json({
          error: 'Refresh token is required',
        });
        return;
      }

      // Verify refresh token
      const payload = verifyRefreshToken(refreshToken);

      // Generate new access token
      const { generateTokens } = await import('@/utils/jwt');
      const { accessToken } = generateTokens({
        userId: payload.userId,
        email: payload.email,
        username: payload.username,
      });

      res.status(200).json({
        token: accessToken,
      });
    } catch (error) {
      logger.error('Token refresh failed:', error);
      res.status(401).json({
        error: 'Invalid refresh token',
      });
    }
  }

  async getProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const user = (req as AuthenticatedRequest).user;
      const profile = await authService.getUserProfile(user.id);

      res.status(200).json({
        user: profile,
      });
    } catch (error) {
      next(error);
    }
  }

  async updateProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const user = (req as AuthenticatedRequest).user;
      const data: UpdateProfileInput = req.body;
      const updatedUser = await authService.updateProfile(user.id, data);

      res.status(200).json({
        message: 'Profile updated successfully',
        user: updatedUser,
      });
    } catch (error) {
      next(error);
    }
  }

  async updateStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const user = (req as AuthenticatedRequest).user;
      const { status } = req.body;
      await authService.updateStatus(user.id, status);

      res.status(200).json({
        message: 'Status updated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async searchUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = (req as AuthenticatedRequest).user;
      const { q: query, limit = 20 } = req.query;

      if (!query || typeof query !== 'string') {
        res.status(400).json({
          error: 'Search query is required',
        });
        return;
      }

      const users = await authService.searchUsers(query, user.id, Number(limit));

      res.status(200).json({
        users,
      });
    } catch (error) {
      next(error);
    }
  }

  async forgotPassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { email } = req.body;
      await authService.initiatePasswordReset(email);
      
      res.status(200).json({
        message: 'If an account with that email exists, a password reset link has been sent.',
      });
    } catch (error) {
      next(error);
    }
  }

  async resetPassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { token, newPassword } = req.body;
      await authService.resetPassword(token, newPassword);
      
      res.status(200).json({
        message: 'Password reset successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
