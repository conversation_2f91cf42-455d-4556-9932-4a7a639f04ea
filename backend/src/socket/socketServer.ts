import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HTTPServer } from 'http';
import jwt from 'jsonwebtoken';
import { logger } from '@/utils/logger';
import { prisma } from '@/config/database';
import { CustomError } from '@/middleware/errorHandler';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: {
    id: string;
    email: string;
    username: string;
  };
}

export class SocketServer {
  private io: SocketIOServer;
  private connectedUsers: Map<string, string> = new Map(); // userId -> socketId

  constructor(httpServer: HTTPServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true,
      },
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: Socket, next) => {
      try {
        const token = socket.handshake.auth.token;

        if (!token) {
          throw new CustomError('Authentication token required', 401);
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

        // Get user from database
        const user = await prisma.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            email: true,
            username: true,
            status: true,
          },
        });

        if (!user) {
          throw new CustomError('User not found', 404);
        }

        // Attach user to socket
        (socket as any).userId = user.id;
        (socket as any).user = user;

        next();
      } catch (error) {
        logger.error('Socket authentication failed:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: Socket) => {
      const authSocket = socket as AuthenticatedSocket;
      logger.info('User connected via socket', {
        userId: authSocket.userId,
        socketId: authSocket.id
      });

      // Store user connection
      this.connectedUsers.set(authSocket.userId, authSocket.id);

      // Update user status to online
      this.updateUserStatus(authSocket.userId, 'online');

      // Join user to their personal room for direct messages
      authSocket.join(`user:${authSocket.userId}`);

      // Join user to their chat rooms
      this.joinUserRooms(authSocket);

      // Handle joining a room
      socket.on('join_room', async (data: { roomId: string }) => {
        try {
          await this.handleJoinRoom(authSocket, data.roomId);
        } catch (error) {
          logger.error('Error joining room:', error);
          socket.emit('error', { message: 'Failed to join room' });
        }
      });

      // Handle leaving a room
      socket.on('leave_room', async (data: { roomId: string }) => {
        try {
          await this.handleLeaveRoom(authSocket, data.roomId);
        } catch (error) {
          logger.error('Error leaving room:', error);
          socket.emit('error', { message: 'Failed to leave room' });
        }
      });

      // Handle sending a message
      socket.on('send_message', async (data: {
        roomId: string;
        content: string;
        type?: 'text' | 'image' | 'file';
      }) => {
        try {
          await this.handleSendMessage(authSocket, data);
        } catch (error) {
          logger.error('Error sending message:', error);
          socket.emit('error', { message: 'Failed to send message' });
        }
      });

      // Handle typing indicators
      socket.on('typing_start', (data: { roomId: string }) => {
        socket.to(`room:${data.roomId}`).emit('user_typing', {
          userId: authSocket.userId,
          username: authSocket.user.username,
          roomId: data.roomId,
        });
      });

      socket.on('typing_stop', (data: { roomId: string }) => {
        socket.to(`room:${data.roomId}`).emit('user_stopped_typing', {
          userId: authSocket.userId,
          roomId: data.roomId,
        });
      });

      // Handle message reactions
      socket.on('add_reaction', async (data: {
        messageId: string;
        emoji: string;
      }) => {
        try {
          await this.handleAddReaction(authSocket, data);
        } catch (error) {
          logger.error('Error adding reaction:', error);
          socket.emit('error', { message: 'Failed to add reaction' });
        }
      });

      // Handle status updates
      socket.on('update_status', async (data: {
        status: 'online' | 'away' | 'busy' | 'offline';
      }) => {
        try {
          await this.updateUserStatus(authSocket.userId, data.status);

          // Broadcast status update to all connected users
          this.io.emit('user_status_updated', {
            userId: authSocket.userId,
            status: data.status,
          });
        } catch (error) {
          logger.error('Error updating status:', error);
          socket.emit('error', { message: 'Failed to update status' });
        }
      });

      // Handle WebRTC signaling for calls
      socket.on('call_offer', (data: {
        targetUserId: string;
        offer: any;
        roomId?: string;
      }) => {
        const targetSocketId = this.connectedUsers.get(data.targetUserId);
        if (targetSocketId) {
          this.io.to(targetSocketId).emit('call_offer', {
            fromUserId: authSocket.userId,
            fromUsername: authSocket.user.username,
            offer: data.offer,
            roomId: data.roomId,
          });
        }
      });

      socket.on('call_answer', (data: {
        targetUserId: string;
        answer: any;
      }) => {
        const targetSocketId = this.connectedUsers.get(data.targetUserId);
        if (targetSocketId) {
          this.io.to(targetSocketId).emit('call_answer', {
            fromUserId: authSocket.userId,
            answer: data.answer,
          });
        }
      });

      socket.on('ice_candidate', (data: {
        targetUserId: string;
        candidate: any;
      }) => {
        const targetSocketId = this.connectedUsers.get(data.targetUserId);
        if (targetSocketId) {
          this.io.to(targetSocketId).emit('ice_candidate', {
            fromUserId: authSocket.userId,
            candidate: data.candidate,
          });
        }
      });

      socket.on('call_end', (data: { targetUserId: string }) => {
        const targetSocketId = this.connectedUsers.get(data.targetUserId);
        if (targetSocketId) {
          this.io.to(targetSocketId).emit('call_end', {
            fromUserId: authSocket.userId,
          });
        }
      });

      // Handle disconnection
      socket.on('disconnect', async () => {
        logger.info('User disconnected', {
          userId: authSocket.userId,
          socketId: authSocket.id
        });

        // Remove user connection
        this.connectedUsers.delete(authSocket.userId);

        // Update user status to offline
        await this.updateUserStatus(authSocket.userId, 'offline');

        // Broadcast user offline status
        this.io.emit('user_status_updated', {
          userId: authSocket.userId,
          status: 'offline',
        });
      });
    });
  }

  private async joinUserRooms(socket: AuthenticatedSocket) {
    try {
      // Get all rooms the user is a member of
      const userRooms = await prisma.roomMember.findMany({
        where: { userId: socket.userId },
        include: {
          room: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      // Join each room
      for (const membership of userRooms) {
        socket.join(`room:${membership.room.id}`);
        logger.debug('User joined room', {
          userId: socket.userId,
          roomId: membership.room.id,
          roomName: membership.room.name,
        });
      }
    } catch (error) {
      logger.error('Error joining user rooms:', error);
    }
  }

  private async handleJoinRoom(socket: AuthenticatedSocket, roomId: string) {
    // Verify user has access to the room
    const membership = await prisma.roomMember.findFirst({
      where: {
        userId: socket.userId,
        roomId: roomId,
      },
    });

    if (!membership) {
      throw new CustomError('Access denied to room', 403);
    }

    socket.join(`room:${roomId}`);
    
    // Notify other room members
    socket.to(`room:${roomId}`).emit('user_joined_room', {
      userId: socket.userId,
      username: socket.user.username,
      roomId: roomId,
    });

    logger.info('User joined room', {
      userId: socket.userId,
      roomId: roomId,
    });
  }

  private async handleLeaveRoom(socket: AuthenticatedSocket, roomId: string) {
    socket.leave(`room:${roomId}`);

    // Notify other room members
    socket.to(`room:${roomId}`).emit('user_left_room', {
      userId: socket.userId,
      username: socket.user.username,
      roomId: roomId,
    });

    logger.info('User left room', {
      userId: socket.userId,
      roomId: roomId,
    });
  }

  private async handleSendMessage(
    socket: AuthenticatedSocket,
    data: { roomId: string; content: string; type?: 'text' | 'image' | 'file' }
  ) {
    // Verify user has access to the room
    const membership = await prisma.roomMember.findFirst({
      where: {
        userId: socket.userId,
        roomId: data.roomId,
      },
    });

    if (!membership) {
      throw new CustomError('Access denied to room', 403);
    }

    // Create message in database
    const message = await prisma.message.create({
      data: {
        content: data.content,
        messageType: data.type || 'text',
        senderId: socket.userId,
        roomId: data.roomId,
      },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            avatarUrl: true,
          },
        },
      },
    });

    // Broadcast message to all room members
    this.io.to(`room:${data.roomId}`).emit('new_message', {
      id: message.id,
      content: message.content,
      type: message.messageType,
      senderId: message.senderId,
      sender: message.sender,
      roomId: message.roomId,
      createdAt: message.createdAt,
    });

    logger.info('Message sent', {
      messageId: message.id,
      senderId: socket.userId,
      roomId: data.roomId,
    });
  }

  private async handleAddReaction(
    socket: AuthenticatedSocket,
    data: { messageId: string; emoji: string }
  ) {
    // Check if message exists and user has access
    const message = await prisma.message.findFirst({
      where: {
        id: data.messageId,
        room: {
          members: {
            some: {
              userId: socket.userId,
            },
          },
        },
      },
    });

    if (!message) {
      throw new CustomError('Message not found or access denied', 404);
    }

    // Add or update reaction
    const reaction = await prisma.messageReaction.upsert({
      where: {
        messageId_userId_emoji: {
          messageId: data.messageId,
          userId: socket.userId,
          emoji: data.emoji,
        },
      },
      update: {
        emoji: data.emoji,
      },
      create: {
        messageId: data.messageId,
        userId: socket.userId,
        emoji: data.emoji,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    });

    // Broadcast reaction to room members
    this.io.to(`room:${message.roomId}`).emit('message_reaction', {
      messageId: data.messageId,
      userId: socket.userId,
      username: reaction.user.username,
      emoji: data.emoji,
    });

    logger.info('Reaction added', {
      messageId: data.messageId,
      userId: socket.userId,
      emoji: data.emoji,
    });
  }

  private async updateUserStatus(
    userId: string,
    status: 'online' | 'away' | 'busy' | 'offline'
  ) {
    try {
      const updateData: any = { status };
      if (status === 'offline') {
        updateData.lastSeen = new Date();
      }

      await prisma.user.update({
        where: { id: userId },
        data: updateData,
      });

      logger.debug('User status updated', { userId, status });
    } catch (error) {
      logger.error('Error updating user status:', error);
    }
  }

  // Public methods for external use
  public emitToUser(userId: string, event: string, data: any) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
    }
  }

  public emitToRoom(roomId: string, event: string, data: any) {
    this.io.to(`room:${roomId}`).emit(event, data);
  }

  public getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  public isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }
}
