import { prisma } from '@/config/database';
import { hashPassword, comparePassword, generateResetToken } from '@/utils/encryption';
import { generateTokens } from '@/utils/jwt';
import { CustomError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import type { RegisterInput, LoginInput, UpdateProfileInput } from '../../../shared/validation/auth';
import type { User, AuthResponse } from '../../../shared/types/user';

export class AuthService {
  async register(data: RegisterInput): Promise<AuthResponse> {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: data.email },
            { username: data.username }
          ]
        }
      });

      if (existingUser) {
        if (existingUser.email === data.email) {
          throw new CustomError('Email already registered', 409);
        }
        if (existingUser.username === data.username) {
          throw new CustomError('Username already taken', 409);
        }
      }

      // Hash password
      const passwordHash = await hashPassword(data.password);

      // Create user
      const user = await prisma.user.create({
        data: {
          email: data.email,
          username: data.username,
          passwordHash,
          firstName: data.firstName,
          lastName: data.lastName,
          status: 'online',
        },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatarUrl: true,
          status: true,
          isVerified: true,
        },
      });

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
      });

      logger.info('User registered successfully', { userId: user.id, email: user.email });

      return {
        user,
        token: accessToken,
        refreshToken,
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  async login(data: LoginInput): Promise<AuthResponse> {
    try {
      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email: data.email },
      });

      if (!user) {
        throw new CustomError('Invalid email or password', 401);
      }

      // Verify password
      const isPasswordValid = await comparePassword(data.password, user.passwordHash);
      if (!isPasswordValid) {
        throw new CustomError('Invalid email or password', 401);
      }

      // Update user status to online
      await prisma.user.update({
        where: { id: user.id },
        data: { 
          status: 'online',
          lastSeen: new Date(),
        },
      });

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
      });

      logger.info('User logged in successfully', { userId: user.id, email: user.email });

      return {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          avatarUrl: user.avatarUrl,
          status: 'online',
          isVerified: user.isVerified,
        },
        token: accessToken,
        refreshToken,
      };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  async logout(userId: string): Promise<void> {
    try {
      // Update user status to offline
      await prisma.user.update({
        where: { id: userId },
        data: { 
          status: 'offline',
          lastSeen: new Date(),
        },
      });

      logger.info('User logged out successfully', { userId });
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  async updateProfile(userId: string, data: UpdateProfileInput): Promise<User> {
    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data: {
          firstName: data.firstName,
          lastName: data.lastName,
        },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatarUrl: true,
          status: true,
          lastSeen: true,
          isVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info('User profile updated successfully', { userId });
      return user;
    } catch (error) {
      logger.error('Profile update failed:', error);
      throw error;
    }
  }

  async updateStatus(userId: string, status: 'online' | 'away' | 'busy' | 'offline'): Promise<void> {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: { 
          status,
          lastSeen: status === 'offline' ? new Date() : undefined,
        },
      });

      logger.info('User status updated successfully', { userId, status });
    } catch (error) {
      logger.error('Status update failed:', error);
      throw error;
    }
  }

  async getUserProfile(userId: string): Promise<User> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatarUrl: true,
          status: true,
          lastSeen: true,
          isVerified: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      return user;
    } catch (error) {
      logger.error('Get user profile failed:', error);
      throw error;
    }
  }

  async searchUsers(query: string, currentUserId: string, limit: number = 20): Promise<User[]> {
    try {
      const users = await prisma.user.findMany({
        where: {
          AND: [
            { id: { not: currentUserId } },
            {
              OR: [
                { username: { contains: query, mode: 'insensitive' } },
                { firstName: { contains: query, mode: 'insensitive' } },
                { lastName: { contains: query, mode: 'insensitive' } },
                { email: { contains: query, mode: 'insensitive' } },
              ],
            },
          ],
        },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatarUrl: true,
          status: true,
          lastSeen: true,
          isVerified: true,
          createdAt: true,
          updatedAt: true,
        },
        take: limit,
      });

      return users;
    } catch (error) {
      logger.error('User search failed:', error);
      throw error;
    }
  }

  async initiatePasswordReset(email: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        // Don't reveal if email exists or not
        logger.info('Password reset requested for non-existent email', { email });
        return;
      }

      // Generate reset token
      const token = generateResetToken();
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Store reset token
      await prisma.passwordResetToken.create({
        data: {
          userId: user.id,
          token,
          expiresAt,
        },
      });

      // TODO: Send email with reset token
      logger.info('Password reset token generated', { userId: user.id, email });
    } catch (error) {
      logger.error('Password reset initiation failed:', error);
      throw error;
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      // Find valid reset token
      const resetToken = await prisma.passwordResetToken.findFirst({
        where: {
          token,
          expiresAt: { gt: new Date() },
          usedAt: null,
        },
        include: {
          user: true,
        },
      });

      if (!resetToken) {
        throw new CustomError('Invalid or expired reset token', 400);
      }

      // Hash new password
      const passwordHash = await hashPassword(newPassword);

      // Update password and mark token as used
      await prisma.$transaction([
        prisma.user.update({
          where: { id: resetToken.userId },
          data: { passwordHash },
        }),
        prisma.passwordResetToken.update({
          where: { id: resetToken.id },
          data: { usedAt: new Date() },
        }),
      ]);

      logger.info('Password reset successfully', { userId: resetToken.userId });
    } catch (error) {
      logger.error('Password reset failed:', error);
      throw error;
    }
  }
}
