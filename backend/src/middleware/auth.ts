import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, extractTokenFromHeader, JwtPayload } from '@/utils/jwt';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    avatarUrl?: string;
    status: string;
    isVerified: boolean;
  };
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
      });
    }

    const payload: JwtPayload = verifyAccessToken(token);
    
    // Fetch user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        avatarUrl: true,
        status: true,
        isVerified: true,
      },
    });

    if (!user) {
      return res.status(401).json({
        error: 'User not found',
      });
    }

    // Attach user to request
    (req as AuthenticatedRequest).user = user;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    return res.status(401).json({
      error: 'Invalid or expired token',
    });
  }
};

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      return next();
    }

    const payload: JwtPayload = verifyAccessToken(token);
    
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        avatarUrl: true,
        status: true,
        isVerified: true,
      },
    });

    if (user) {
      (req as AuthenticatedRequest).user = user;
    }
    
    next();
  } catch (error) {
    // For optional auth, we don't return an error, just continue without user
    logger.debug('Optional auth failed:', error);
    next();
  }
};
