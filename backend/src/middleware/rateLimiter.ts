import rateLimit from 'express-rate-limit';
import { getRedisClient } from '@/config/redis';
import { logger } from '@/utils/logger';

// Create a custom store using Redis if available
const createRedisStore = () => {
  const redisClient = getRedisClient();
  
  if (!redisClient) {
    return undefined; // Fall back to memory store
  }

  return {
    incr: async (key: string) => {
      try {
        const result = await redisClient.incr(key);
        return { totalHits: result, resetTime: undefined };
      } catch (error) {
        logger.error('Redis rate limit incr error:', error);
        throw error;
      }
    },
    decrement: async (key: string) => {
      try {
        await redisClient.decr(key);
      } catch (error) {
        logger.error('Redis rate limit decr error:', error);
      }
    },
    resetKey: async (key: string) => {
      try {
        await redisClient.del(key);
      } catch (error) {
        logger.error('Redis rate limit reset error:', error);
      }
    },
  };
};

// General API rate limiter
export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  store: createRedisStore(),
});

// Strict rate limiter for auth endpoints
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 auth requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  store: createRedisStore(),
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Message sending rate limiter
export const messageLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // Limit each IP to 30 messages per minute
  message: {
    error: 'Too many messages sent, please slow down.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  store: createRedisStore(),
});

// File upload rate limiter
export const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 uploads per 15 minutes
  message: {
    error: 'Too many file uploads, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  store: createRedisStore(),
});

// Password reset rate limiter
export const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: {
    error: 'Too many password reset attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  store: createRedisStore(),
});
