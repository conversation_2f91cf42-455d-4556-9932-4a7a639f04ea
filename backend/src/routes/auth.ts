import { Router } from 'express';
import { AuthController } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import { validateBody, validateQuery } from '@/utils/validation';
import { authLimiter, passwordResetLimiter } from '@/middleware/rateLimiter';
import {
  registerSchema,
  loginSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  updateProfileSchema,
  updateStatusSchema,
} from '../../../shared/validation/auth';
import { searchSchema } from '../../../shared/validation/room';

const router = Router();
const authController = new AuthController();

// Public routes
router.post('/register', 
  authLimiter,
  validateBody(registerSchema),
  authController.register
);

router.post('/login',
  authLimiter,
  validateBody(loginSchema),
  authController.login
);

router.post('/refresh',
  authController.refreshToken
);

router.post('/forgot-password',
  passwordResetLimiter,
  validateBody(forgotPasswordSchema),
  authController.forgotPassword
);

router.post('/reset-password',
  validateBody(resetPasswordSchema),
  authController.resetPassword
);

// Protected routes
router.post('/logout',
  authenticate,
  authController.logout
);

router.get('/profile',
  authenticate,
  authController.getProfile
);

router.put('/profile',
  authenticate,
  validateBody(updateProfileSchema),
  authController.updateProfile
);

router.put('/status',
  authenticate,
  validateBody(updateStatusSchema),
  authController.updateStatus
);

router.get('/users/search',
  authenticate,
  validateQuery(searchSchema),
  authController.searchUsers
);

export default router;
