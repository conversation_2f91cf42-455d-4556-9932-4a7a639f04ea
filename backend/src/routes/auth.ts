import { Router } from 'express';
import { AuthController } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import { validateBody, validateQuery } from '@/utils/validation';
import { authLimiter, passwordResetLimiter } from '@/middleware/rateLimiter';
import {
  registerSchema,
  loginSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  updateProfileSchema,
  updateStatusSchema,
} from '../../../shared/validation/auth';
import { searchSchema } from '../../../shared/validation/room';

const router = Router();
const authController = new AuthController();

// Public routes
router.post('/register', 
  authLimiter,
  validateBody(registerSchema),
  authController.register
);

router.post('/login',
  authLimiter,
  validateBody(loginSchema),
  authController.login
);

router.post('/refresh',
  authController.refreshToken
);

router.post('/forgot-password',
  passwordResetLimiter,
  validateBody(forgotPasswordSchema),
  authController.forgotPassword
);

router.post('/reset-password',
  validateBody(resetPasswordSchema),
  authController.resetPassword
);

// Protected routes
router.post('/logout',
  authenticate,
  authController.logout as any
);

router.get('/profile',
  authenticate,
  authController.getProfile as any
);

router.put('/profile',
  authenticate,
  validateBody(updateProfileSchema),
  authController.updateProfile as any
);

router.put('/status',
  authenticate,
  validateBody(updateStatusSchema),
  authController.updateStatus as any
);

router.get('/users/search',
  authenticate,
  validateQuery(searchSchema),
  authController.searchUsers as any
);

export default router;
