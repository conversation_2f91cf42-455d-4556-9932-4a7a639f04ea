import { Router } from 'express';
import { AuthController } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import { validateQuery } from '@/utils/validation';
import { searchSchema } from '../../../shared/validation/room';

const router = Router();
const authController = new AuthController();

// All user routes require authentication
router.use(authenticate);

// Search users
router.get('/search',
  validateQuery(searchSchema),
  authController.searchUsers
);

// Get current user profile
router.get('/profile',
  authController.getProfile
);

export default router;
