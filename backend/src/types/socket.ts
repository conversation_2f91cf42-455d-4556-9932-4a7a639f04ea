import { Socket } from 'socket.io';

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    avatarUrl?: string;
    status: string;
  };
}

export interface SocketAuthData {
  token: string;
}

export interface JoinRoomData {
  roomId: string;
}

export interface LeaveRoomData {
  roomId: string;
}

export interface SendMessageData {
  roomId: string;
  content: string;
  replyTo?: string;
}

export interface TypingData {
  roomId: string;
}

export interface MessageReadData {
  messageId: string;
}

export interface CallInitiateData {
  roomId: string;
  type: 'audio' | 'video';
}

export interface CallResponseData {
  callId: string;
}

export interface WebRTCSignalingData {
  callId: string;
  offer?: RTCSessionDescription;
  answer?: RTCSessionDescription;
  candidate?: RTCIceCandidate;
}

export interface UpdateStatusData {
  status: 'online' | 'away' | 'busy' | 'offline';
}
