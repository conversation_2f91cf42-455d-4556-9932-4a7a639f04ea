import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';
import { logger } from './logger';

export interface ValidationError {
  field: string;
  message: string;
}

export const formatZodError = (error: z.ZodError): ValidationError[] => {
  return error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
  }));
};

export const validateBody = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = formatZodError(error);
        return res.status(400).json({
          error: 'Validation failed',
          details: validationErrors,
        });
      }
      
      logger.error('Validation error:', error);
      return res.status(500).json({
        error: 'Internal server error',
      });
    }
  };
};

export const validateQuery = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.query);
      req.query = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = formatZodError(error);
        return res.status(400).json({
          error: 'Validation failed',
          details: validationErrors,
        });
      }
      
      logger.error('Query validation error:', error);
      return res.status(500).json({
        error: 'Internal server error',
      });
    }
  };
};

export const validateParams = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.params);
      req.params = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = formatZodError(error);
        return res.status(400).json({
          error: 'Validation failed',
          details: validationErrors,
        });
      }
      
      logger.error('Params validation error:', error);
      return res.status(500).json({
        error: 'Internal server error',
      });
    }
  };
};
