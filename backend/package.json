{"name": "chat-app-backend", "version": "1.0.0", "description": "Backend for chat application with real-time messaging and WebRTC", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec \"ts-node -r tsconfig-paths/register\" src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.7.0", "aws-sdk": "^2.1498.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "prisma": "^5.7.0", "redis": "^4.6.11", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.6.0", "typescript": "^5.2.2"}, "prisma": {"seed": "tsx prisma/seed.ts"}}