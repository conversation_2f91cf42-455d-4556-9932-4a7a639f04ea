import {Request} from '../lib/request';
import {Response} from '../lib/response';
import {AWSError} from '../lib/error';
import {Service} from '../lib/service';
import {WaiterConfiguration} from '../lib/service';
import {ServiceConfigurationOptions} from '../lib/service';
import {ConfigBase as Config} from '../lib/config-base';
interface Blob {}
declare class SES extends Service {
  /**
   * Constructs a service object. This object has one method for each API operation.
   */
  constructor(options?: SES.Types.ClientConfiguration)
  config: Config & SES.Types.ClientConfiguration;
  /**
   * Creates a receipt rule set by cloning an existing one. All receipt rules and configurations are copied to the new receipt rule set and are completely independent of the source rule set. For information about setting up rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  cloneReceiptRuleSet(params: SES.Types.CloneReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.CloneReceiptRuleSetResponse) => void): Request<SES.Types.CloneReceiptRuleSetResponse, AWSError>;
  /**
   * Creates a receipt rule set by cloning an existing one. All receipt rules and configurations are copied to the new receipt rule set and are completely independent of the source rule set. For information about setting up rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  cloneReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.CloneReceiptRuleSetResponse) => void): Request<SES.Types.CloneReceiptRuleSetResponse, AWSError>;
  /**
   * Creates a configuration set. Configuration sets enable you to publish email sending events. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createConfigurationSet(params: SES.Types.CreateConfigurationSetRequest, callback?: (err: AWSError, data: SES.Types.CreateConfigurationSetResponse) => void): Request<SES.Types.CreateConfigurationSetResponse, AWSError>;
  /**
   * Creates a configuration set. Configuration sets enable you to publish email sending events. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createConfigurationSet(callback?: (err: AWSError, data: SES.Types.CreateConfigurationSetResponse) => void): Request<SES.Types.CreateConfigurationSetResponse, AWSError>;
  /**
   * Creates a configuration set event destination.  When you create or update an event destination, you must provide one, and only one, destination. The destination can be CloudWatch, Amazon Kinesis Firehose, or Amazon Simple Notification Service (Amazon SNS).  An event destination is the Amazon Web Services service to which Amazon SES publishes the email sending events associated with a configuration set. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createConfigurationSetEventDestination(params: SES.Types.CreateConfigurationSetEventDestinationRequest, callback?: (err: AWSError, data: SES.Types.CreateConfigurationSetEventDestinationResponse) => void): Request<SES.Types.CreateConfigurationSetEventDestinationResponse, AWSError>;
  /**
   * Creates a configuration set event destination.  When you create or update an event destination, you must provide one, and only one, destination. The destination can be CloudWatch, Amazon Kinesis Firehose, or Amazon Simple Notification Service (Amazon SNS).  An event destination is the Amazon Web Services service to which Amazon SES publishes the email sending events associated with a configuration set. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createConfigurationSetEventDestination(callback?: (err: AWSError, data: SES.Types.CreateConfigurationSetEventDestinationResponse) => void): Request<SES.Types.CreateConfigurationSetEventDestinationResponse, AWSError>;
  /**
   * Creates an association between a configuration set and a custom domain for open and click event tracking.  By default, images and links used for tracking open and click events are hosted on domains operated by Amazon SES. You can configure a subdomain of your own to handle these events. For information about using custom domains, see the Amazon SES Developer Guide.
   */
  createConfigurationSetTrackingOptions(params: SES.Types.CreateConfigurationSetTrackingOptionsRequest, callback?: (err: AWSError, data: SES.Types.CreateConfigurationSetTrackingOptionsResponse) => void): Request<SES.Types.CreateConfigurationSetTrackingOptionsResponse, AWSError>;
  /**
   * Creates an association between a configuration set and a custom domain for open and click event tracking.  By default, images and links used for tracking open and click events are hosted on domains operated by Amazon SES. You can configure a subdomain of your own to handle these events. For information about using custom domains, see the Amazon SES Developer Guide.
   */
  createConfigurationSetTrackingOptions(callback?: (err: AWSError, data: SES.Types.CreateConfigurationSetTrackingOptionsResponse) => void): Request<SES.Types.CreateConfigurationSetTrackingOptionsResponse, AWSError>;
  /**
   * Creates a new custom verification email template. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createCustomVerificationEmailTemplate(params: SES.Types.CreateCustomVerificationEmailTemplateRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates a new custom verification email template. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createCustomVerificationEmailTemplate(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates a new IP address filter. For information about setting up IP address filters, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createReceiptFilter(params: SES.Types.CreateReceiptFilterRequest, callback?: (err: AWSError, data: SES.Types.CreateReceiptFilterResponse) => void): Request<SES.Types.CreateReceiptFilterResponse, AWSError>;
  /**
   * Creates a new IP address filter. For information about setting up IP address filters, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createReceiptFilter(callback?: (err: AWSError, data: SES.Types.CreateReceiptFilterResponse) => void): Request<SES.Types.CreateReceiptFilterResponse, AWSError>;
  /**
   * Creates a receipt rule. For information about setting up receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createReceiptRule(params: SES.Types.CreateReceiptRuleRequest, callback?: (err: AWSError, data: SES.Types.CreateReceiptRuleResponse) => void): Request<SES.Types.CreateReceiptRuleResponse, AWSError>;
  /**
   * Creates a receipt rule. For information about setting up receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createReceiptRule(callback?: (err: AWSError, data: SES.Types.CreateReceiptRuleResponse) => void): Request<SES.Types.CreateReceiptRuleResponse, AWSError>;
  /**
   * Creates an empty receipt rule set. For information about setting up receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createReceiptRuleSet(params: SES.Types.CreateReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.CreateReceiptRuleSetResponse) => void): Request<SES.Types.CreateReceiptRuleSetResponse, AWSError>;
  /**
   * Creates an empty receipt rule set. For information about setting up receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.CreateReceiptRuleSetResponse) => void): Request<SES.Types.CreateReceiptRuleSetResponse, AWSError>;
  /**
   * Creates an email template. Email templates enable you to send personalized email to one or more destinations in a single operation. For more information, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createTemplate(params: SES.Types.CreateTemplateRequest, callback?: (err: AWSError, data: SES.Types.CreateTemplateResponse) => void): Request<SES.Types.CreateTemplateResponse, AWSError>;
  /**
   * Creates an email template. Email templates enable you to send personalized email to one or more destinations in a single operation. For more information, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  createTemplate(callback?: (err: AWSError, data: SES.Types.CreateTemplateResponse) => void): Request<SES.Types.CreateTemplateResponse, AWSError>;
  /**
   * Deletes a configuration set. Configuration sets enable you to publish email sending events. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteConfigurationSet(params: SES.Types.DeleteConfigurationSetRequest, callback?: (err: AWSError, data: SES.Types.DeleteConfigurationSetResponse) => void): Request<SES.Types.DeleteConfigurationSetResponse, AWSError>;
  /**
   * Deletes a configuration set. Configuration sets enable you to publish email sending events. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteConfigurationSet(callback?: (err: AWSError, data: SES.Types.DeleteConfigurationSetResponse) => void): Request<SES.Types.DeleteConfigurationSetResponse, AWSError>;
  /**
   * Deletes a configuration set event destination. Configuration set event destinations are associated with configuration sets, which enable you to publish email sending events. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteConfigurationSetEventDestination(params: SES.Types.DeleteConfigurationSetEventDestinationRequest, callback?: (err: AWSError, data: SES.Types.DeleteConfigurationSetEventDestinationResponse) => void): Request<SES.Types.DeleteConfigurationSetEventDestinationResponse, AWSError>;
  /**
   * Deletes a configuration set event destination. Configuration set event destinations are associated with configuration sets, which enable you to publish email sending events. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteConfigurationSetEventDestination(callback?: (err: AWSError, data: SES.Types.DeleteConfigurationSetEventDestinationResponse) => void): Request<SES.Types.DeleteConfigurationSetEventDestinationResponse, AWSError>;
  /**
   * Deletes an association between a configuration set and a custom domain for open and click event tracking. By default, images and links used for tracking open and click events are hosted on domains operated by Amazon SES. You can configure a subdomain of your own to handle these events. For information about using custom domains, see the Amazon SES Developer Guide.  Deleting this kind of association results in emails sent using the specified configuration set to capture open and click events using the standard, Amazon SES-operated domains. 
   */
  deleteConfigurationSetTrackingOptions(params: SES.Types.DeleteConfigurationSetTrackingOptionsRequest, callback?: (err: AWSError, data: SES.Types.DeleteConfigurationSetTrackingOptionsResponse) => void): Request<SES.Types.DeleteConfigurationSetTrackingOptionsResponse, AWSError>;
  /**
   * Deletes an association between a configuration set and a custom domain for open and click event tracking. By default, images and links used for tracking open and click events are hosted on domains operated by Amazon SES. You can configure a subdomain of your own to handle these events. For information about using custom domains, see the Amazon SES Developer Guide.  Deleting this kind of association results in emails sent using the specified configuration set to capture open and click events using the standard, Amazon SES-operated domains. 
   */
  deleteConfigurationSetTrackingOptions(callback?: (err: AWSError, data: SES.Types.DeleteConfigurationSetTrackingOptionsResponse) => void): Request<SES.Types.DeleteConfigurationSetTrackingOptionsResponse, AWSError>;
  /**
   * Deletes an existing custom verification email template.  For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteCustomVerificationEmailTemplate(params: SES.Types.DeleteCustomVerificationEmailTemplateRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes an existing custom verification email template.  For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteCustomVerificationEmailTemplate(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified identity (an email address or a domain) from the list of verified identities. You can execute this operation no more than once per second.
   */
  deleteIdentity(params: SES.Types.DeleteIdentityRequest, callback?: (err: AWSError, data: SES.Types.DeleteIdentityResponse) => void): Request<SES.Types.DeleteIdentityResponse, AWSError>;
  /**
   * Deletes the specified identity (an email address or a domain) from the list of verified identities. You can execute this operation no more than once per second.
   */
  deleteIdentity(callback?: (err: AWSError, data: SES.Types.DeleteIdentityResponse) => void): Request<SES.Types.DeleteIdentityResponse, AWSError>;
  /**
   * Deletes the specified sending authorization policy for the given identity (an email address or a domain). This operation returns successfully even if a policy with the specified name does not exist.  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteIdentityPolicy(params: SES.Types.DeleteIdentityPolicyRequest, callback?: (err: AWSError, data: SES.Types.DeleteIdentityPolicyResponse) => void): Request<SES.Types.DeleteIdentityPolicyResponse, AWSError>;
  /**
   * Deletes the specified sending authorization policy for the given identity (an email address or a domain). This operation returns successfully even if a policy with the specified name does not exist.  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteIdentityPolicy(callback?: (err: AWSError, data: SES.Types.DeleteIdentityPolicyResponse) => void): Request<SES.Types.DeleteIdentityPolicyResponse, AWSError>;
  /**
   * Deletes the specified IP address filter. For information about managing IP address filters, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteReceiptFilter(params: SES.Types.DeleteReceiptFilterRequest, callback?: (err: AWSError, data: SES.Types.DeleteReceiptFilterResponse) => void): Request<SES.Types.DeleteReceiptFilterResponse, AWSError>;
  /**
   * Deletes the specified IP address filter. For information about managing IP address filters, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteReceiptFilter(callback?: (err: AWSError, data: SES.Types.DeleteReceiptFilterResponse) => void): Request<SES.Types.DeleteReceiptFilterResponse, AWSError>;
  /**
   * Deletes the specified receipt rule. For information about managing receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteReceiptRule(params: SES.Types.DeleteReceiptRuleRequest, callback?: (err: AWSError, data: SES.Types.DeleteReceiptRuleResponse) => void): Request<SES.Types.DeleteReceiptRuleResponse, AWSError>;
  /**
   * Deletes the specified receipt rule. For information about managing receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteReceiptRule(callback?: (err: AWSError, data: SES.Types.DeleteReceiptRuleResponse) => void): Request<SES.Types.DeleteReceiptRuleResponse, AWSError>;
  /**
   * Deletes the specified receipt rule set and all of the receipt rules it contains.  The currently active rule set cannot be deleted.  For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteReceiptRuleSet(params: SES.Types.DeleteReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.DeleteReceiptRuleSetResponse) => void): Request<SES.Types.DeleteReceiptRuleSetResponse, AWSError>;
  /**
   * Deletes the specified receipt rule set and all of the receipt rules it contains.  The currently active rule set cannot be deleted.  For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  deleteReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.DeleteReceiptRuleSetResponse) => void): Request<SES.Types.DeleteReceiptRuleSetResponse, AWSError>;
  /**
   * Deletes an email template. You can execute this operation no more than once per second.
   */
  deleteTemplate(params: SES.Types.DeleteTemplateRequest, callback?: (err: AWSError, data: SES.Types.DeleteTemplateResponse) => void): Request<SES.Types.DeleteTemplateResponse, AWSError>;
  /**
   * Deletes an email template. You can execute this operation no more than once per second.
   */
  deleteTemplate(callback?: (err: AWSError, data: SES.Types.DeleteTemplateResponse) => void): Request<SES.Types.DeleteTemplateResponse, AWSError>;
  /**
   * Deprecated. Use the DeleteIdentity operation to delete email addresses and domains.
   */
  deleteVerifiedEmailAddress(params: SES.Types.DeleteVerifiedEmailAddressRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deprecated. Use the DeleteIdentity operation to delete email addresses and domains.
   */
  deleteVerifiedEmailAddress(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Returns the metadata and receipt rules for the receipt rule set that is currently active. For information about setting up receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeActiveReceiptRuleSet(params: SES.Types.DescribeActiveReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.DescribeActiveReceiptRuleSetResponse) => void): Request<SES.Types.DescribeActiveReceiptRuleSetResponse, AWSError>;
  /**
   * Returns the metadata and receipt rules for the receipt rule set that is currently active. For information about setting up receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeActiveReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.DescribeActiveReceiptRuleSetResponse) => void): Request<SES.Types.DescribeActiveReceiptRuleSetResponse, AWSError>;
  /**
   * Returns the details of the specified configuration set. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeConfigurationSet(params: SES.Types.DescribeConfigurationSetRequest, callback?: (err: AWSError, data: SES.Types.DescribeConfigurationSetResponse) => void): Request<SES.Types.DescribeConfigurationSetResponse, AWSError>;
  /**
   * Returns the details of the specified configuration set. For information about using configuration sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeConfigurationSet(callback?: (err: AWSError, data: SES.Types.DescribeConfigurationSetResponse) => void): Request<SES.Types.DescribeConfigurationSetResponse, AWSError>;
  /**
   * Returns the details of the specified receipt rule. For information about setting up receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeReceiptRule(params: SES.Types.DescribeReceiptRuleRequest, callback?: (err: AWSError, data: SES.Types.DescribeReceiptRuleResponse) => void): Request<SES.Types.DescribeReceiptRuleResponse, AWSError>;
  /**
   * Returns the details of the specified receipt rule. For information about setting up receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeReceiptRule(callback?: (err: AWSError, data: SES.Types.DescribeReceiptRuleResponse) => void): Request<SES.Types.DescribeReceiptRuleResponse, AWSError>;
  /**
   * Returns the details of the specified receipt rule set. For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeReceiptRuleSet(params: SES.Types.DescribeReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.DescribeReceiptRuleSetResponse) => void): Request<SES.Types.DescribeReceiptRuleSetResponse, AWSError>;
  /**
   * Returns the details of the specified receipt rule set. For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  describeReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.DescribeReceiptRuleSetResponse) => void): Request<SES.Types.DescribeReceiptRuleSetResponse, AWSError>;
  /**
   * Returns the email sending status of the Amazon SES account for the current Region. You can execute this operation no more than once per second.
   */
  getAccountSendingEnabled(callback?: (err: AWSError, data: SES.Types.GetAccountSendingEnabledResponse) => void): Request<SES.Types.GetAccountSendingEnabledResponse, AWSError>;
  /**
   * Returns the custom email verification template for the template name you specify. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  getCustomVerificationEmailTemplate(params: SES.Types.GetCustomVerificationEmailTemplateRequest, callback?: (err: AWSError, data: SES.Types.GetCustomVerificationEmailTemplateResponse) => void): Request<SES.Types.GetCustomVerificationEmailTemplateResponse, AWSError>;
  /**
   * Returns the custom email verification template for the template name you specify. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  getCustomVerificationEmailTemplate(callback?: (err: AWSError, data: SES.Types.GetCustomVerificationEmailTemplateResponse) => void): Request<SES.Types.GetCustomVerificationEmailTemplateResponse, AWSError>;
  /**
   * Returns the current status of Easy DKIM signing for an entity. For domain name identities, this operation also returns the DKIM tokens that are required for Easy DKIM signing, and whether Amazon SES has successfully verified that these tokens have been published. This operation takes a list of identities as input and returns the following information for each:   Whether Easy DKIM signing is enabled or disabled.   A set of DKIM tokens that represent the identity. If the identity is an email address, the tokens represent the domain of that address.   Whether Amazon SES has successfully verified the DKIM tokens published in the domain's DNS. This information is only returned for domain name identities, not for email addresses.   This operation is throttled at one request per second and can only get DKIM attributes for up to 100 identities at a time. For more information about creating DNS records using DKIM tokens, go to the Amazon SES Developer Guide.
   */
  getIdentityDkimAttributes(params: SES.Types.GetIdentityDkimAttributesRequest, callback?: (err: AWSError, data: SES.Types.GetIdentityDkimAttributesResponse) => void): Request<SES.Types.GetIdentityDkimAttributesResponse, AWSError>;
  /**
   * Returns the current status of Easy DKIM signing for an entity. For domain name identities, this operation also returns the DKIM tokens that are required for Easy DKIM signing, and whether Amazon SES has successfully verified that these tokens have been published. This operation takes a list of identities as input and returns the following information for each:   Whether Easy DKIM signing is enabled or disabled.   A set of DKIM tokens that represent the identity. If the identity is an email address, the tokens represent the domain of that address.   Whether Amazon SES has successfully verified the DKIM tokens published in the domain's DNS. This information is only returned for domain name identities, not for email addresses.   This operation is throttled at one request per second and can only get DKIM attributes for up to 100 identities at a time. For more information about creating DNS records using DKIM tokens, go to the Amazon SES Developer Guide.
   */
  getIdentityDkimAttributes(callback?: (err: AWSError, data: SES.Types.GetIdentityDkimAttributesResponse) => void): Request<SES.Types.GetIdentityDkimAttributesResponse, AWSError>;
  /**
   * Returns the custom MAIL FROM attributes for a list of identities (email addresses : domains). This operation is throttled at one request per second and can only get custom MAIL FROM attributes for up to 100 identities at a time.
   */
  getIdentityMailFromDomainAttributes(params: SES.Types.GetIdentityMailFromDomainAttributesRequest, callback?: (err: AWSError, data: SES.Types.GetIdentityMailFromDomainAttributesResponse) => void): Request<SES.Types.GetIdentityMailFromDomainAttributesResponse, AWSError>;
  /**
   * Returns the custom MAIL FROM attributes for a list of identities (email addresses : domains). This operation is throttled at one request per second and can only get custom MAIL FROM attributes for up to 100 identities at a time.
   */
  getIdentityMailFromDomainAttributes(callback?: (err: AWSError, data: SES.Types.GetIdentityMailFromDomainAttributesResponse) => void): Request<SES.Types.GetIdentityMailFromDomainAttributesResponse, AWSError>;
  /**
   * Given a list of verified identities (email addresses and/or domains), returns a structure describing identity notification attributes. This operation is throttled at one request per second and can only get notification attributes for up to 100 identities at a time. For more information about using notifications with Amazon SES, see the Amazon SES Developer Guide.
   */
  getIdentityNotificationAttributes(params: SES.Types.GetIdentityNotificationAttributesRequest, callback?: (err: AWSError, data: SES.Types.GetIdentityNotificationAttributesResponse) => void): Request<SES.Types.GetIdentityNotificationAttributesResponse, AWSError>;
  /**
   * Given a list of verified identities (email addresses and/or domains), returns a structure describing identity notification attributes. This operation is throttled at one request per second and can only get notification attributes for up to 100 identities at a time. For more information about using notifications with Amazon SES, see the Amazon SES Developer Guide.
   */
  getIdentityNotificationAttributes(callback?: (err: AWSError, data: SES.Types.GetIdentityNotificationAttributesResponse) => void): Request<SES.Types.GetIdentityNotificationAttributesResponse, AWSError>;
  /**
   * Returns the requested sending authorization policies for the given identity (an email address or a domain). The policies are returned as a map of policy names to policy contents. You can retrieve a maximum of 20 policies at a time.  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  getIdentityPolicies(params: SES.Types.GetIdentityPoliciesRequest, callback?: (err: AWSError, data: SES.Types.GetIdentityPoliciesResponse) => void): Request<SES.Types.GetIdentityPoliciesResponse, AWSError>;
  /**
   * Returns the requested sending authorization policies for the given identity (an email address or a domain). The policies are returned as a map of policy names to policy contents. You can retrieve a maximum of 20 policies at a time.  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  getIdentityPolicies(callback?: (err: AWSError, data: SES.Types.GetIdentityPoliciesResponse) => void): Request<SES.Types.GetIdentityPoliciesResponse, AWSError>;
  /**
   * Given a list of identities (email addresses and/or domains), returns the verification status and (for domain identities) the verification token for each identity. The verification status of an email address is "Pending" until the email address owner clicks the link within the verification email that Amazon SES sent to that address. If the email address owner clicks the link within 24 hours, the verification status of the email address changes to "Success". If the link is not clicked within 24 hours, the verification status changes to "Failed." In that case, to verify the email address, you must restart the verification process from the beginning. For domain identities, the domain's verification status is "Pending" as Amazon SES searches for the required TXT record in the DNS settings of the domain. When Amazon SES detects the record, the domain's verification status changes to "Success". If Amazon SES is unable to detect the record within 72 hours, the domain's verification status changes to "Failed." In that case, to verify the domain, you must restart the verification process from the beginning. This operation is throttled at one request per second and can only get verification attributes for up to 100 identities at a time.
   */
  getIdentityVerificationAttributes(params: SES.Types.GetIdentityVerificationAttributesRequest, callback?: (err: AWSError, data: SES.Types.GetIdentityVerificationAttributesResponse) => void): Request<SES.Types.GetIdentityVerificationAttributesResponse, AWSError>;
  /**
   * Given a list of identities (email addresses and/or domains), returns the verification status and (for domain identities) the verification token for each identity. The verification status of an email address is "Pending" until the email address owner clicks the link within the verification email that Amazon SES sent to that address. If the email address owner clicks the link within 24 hours, the verification status of the email address changes to "Success". If the link is not clicked within 24 hours, the verification status changes to "Failed." In that case, to verify the email address, you must restart the verification process from the beginning. For domain identities, the domain's verification status is "Pending" as Amazon SES searches for the required TXT record in the DNS settings of the domain. When Amazon SES detects the record, the domain's verification status changes to "Success". If Amazon SES is unable to detect the record within 72 hours, the domain's verification status changes to "Failed." In that case, to verify the domain, you must restart the verification process from the beginning. This operation is throttled at one request per second and can only get verification attributes for up to 100 identities at a time.
   */
  getIdentityVerificationAttributes(callback?: (err: AWSError, data: SES.Types.GetIdentityVerificationAttributesResponse) => void): Request<SES.Types.GetIdentityVerificationAttributesResponse, AWSError>;
  /**
   * Provides the sending limits for the Amazon SES account.  You can execute this operation no more than once per second.
   */
  getSendQuota(callback?: (err: AWSError, data: SES.Types.GetSendQuotaResponse) => void): Request<SES.Types.GetSendQuotaResponse, AWSError>;
  /**
   * Provides sending statistics for the current Amazon Web Services Region. The result is a list of data points, representing the last two weeks of sending activity. Each data point in the list contains statistics for a 15-minute period of time. You can execute this operation no more than once per second.
   */
  getSendStatistics(callback?: (err: AWSError, data: SES.Types.GetSendStatisticsResponse) => void): Request<SES.Types.GetSendStatisticsResponse, AWSError>;
  /**
   * Displays the template object (which includes the Subject line, HTML part and text part) for the template you specify. You can execute this operation no more than once per second.
   */
  getTemplate(params: SES.Types.GetTemplateRequest, callback?: (err: AWSError, data: SES.Types.GetTemplateResponse) => void): Request<SES.Types.GetTemplateResponse, AWSError>;
  /**
   * Displays the template object (which includes the Subject line, HTML part and text part) for the template you specify. You can execute this operation no more than once per second.
   */
  getTemplate(callback?: (err: AWSError, data: SES.Types.GetTemplateResponse) => void): Request<SES.Types.GetTemplateResponse, AWSError>;
  /**
   * Provides a list of the configuration sets associated with your Amazon SES account in the current Amazon Web Services Region. For information about using configuration sets, see Monitoring Your Amazon SES Sending Activity in the Amazon SES Developer Guide.  You can execute this operation no more than once per second. This operation returns up to 1,000 configuration sets each time it is run. If your Amazon SES account has more than 1,000 configuration sets, this operation also returns NextToken. You can then execute the ListConfigurationSets operation again, passing the NextToken parameter and the value of the NextToken element to retrieve additional results.
   */
  listConfigurationSets(params: SES.Types.ListConfigurationSetsRequest, callback?: (err: AWSError, data: SES.Types.ListConfigurationSetsResponse) => void): Request<SES.Types.ListConfigurationSetsResponse, AWSError>;
  /**
   * Provides a list of the configuration sets associated with your Amazon SES account in the current Amazon Web Services Region. For information about using configuration sets, see Monitoring Your Amazon SES Sending Activity in the Amazon SES Developer Guide.  You can execute this operation no more than once per second. This operation returns up to 1,000 configuration sets each time it is run. If your Amazon SES account has more than 1,000 configuration sets, this operation also returns NextToken. You can then execute the ListConfigurationSets operation again, passing the NextToken parameter and the value of the NextToken element to retrieve additional results.
   */
  listConfigurationSets(callback?: (err: AWSError, data: SES.Types.ListConfigurationSetsResponse) => void): Request<SES.Types.ListConfigurationSetsResponse, AWSError>;
  /**
   * Lists the existing custom verification email templates for your account in the current Amazon Web Services Region. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listCustomVerificationEmailTemplates(params: SES.Types.ListCustomVerificationEmailTemplatesRequest, callback?: (err: AWSError, data: SES.Types.ListCustomVerificationEmailTemplatesResponse) => void): Request<SES.Types.ListCustomVerificationEmailTemplatesResponse, AWSError>;
  /**
   * Lists the existing custom verification email templates for your account in the current Amazon Web Services Region. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listCustomVerificationEmailTemplates(callback?: (err: AWSError, data: SES.Types.ListCustomVerificationEmailTemplatesResponse) => void): Request<SES.Types.ListCustomVerificationEmailTemplatesResponse, AWSError>;
  /**
   * Returns a list containing all of the identities (email addresses and domains) for your Amazon Web Services account in the current Amazon Web Services Region, regardless of verification status. You can execute this operation no more than once per second.  It's recommended that for successive pagination calls of this API, you continue to the use the same parameter/value pairs as used in the original call, e.g., if you used IdentityType=Domain in the the original call and received a NextToken in the response, you should continue providing the IdentityType=Domain parameter for further NextToken calls; however, if you didn't provide the IdentityType parameter in the original call, then continue to not provide it for successive pagination calls. Using this protocol will ensure consistent results. 
   */
  listIdentities(params: SES.Types.ListIdentitiesRequest, callback?: (err: AWSError, data: SES.Types.ListIdentitiesResponse) => void): Request<SES.Types.ListIdentitiesResponse, AWSError>;
  /**
   * Returns a list containing all of the identities (email addresses and domains) for your Amazon Web Services account in the current Amazon Web Services Region, regardless of verification status. You can execute this operation no more than once per second.  It's recommended that for successive pagination calls of this API, you continue to the use the same parameter/value pairs as used in the original call, e.g., if you used IdentityType=Domain in the the original call and received a NextToken in the response, you should continue providing the IdentityType=Domain parameter for further NextToken calls; however, if you didn't provide the IdentityType parameter in the original call, then continue to not provide it for successive pagination calls. Using this protocol will ensure consistent results. 
   */
  listIdentities(callback?: (err: AWSError, data: SES.Types.ListIdentitiesResponse) => void): Request<SES.Types.ListIdentitiesResponse, AWSError>;
  /**
   * Returns a list of sending authorization policies that are attached to the given identity (an email address or a domain). This operation returns only a list. To get the actual policy content, use GetIdentityPolicies.  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listIdentityPolicies(params: SES.Types.ListIdentityPoliciesRequest, callback?: (err: AWSError, data: SES.Types.ListIdentityPoliciesResponse) => void): Request<SES.Types.ListIdentityPoliciesResponse, AWSError>;
  /**
   * Returns a list of sending authorization policies that are attached to the given identity (an email address or a domain). This operation returns only a list. To get the actual policy content, use GetIdentityPolicies.  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listIdentityPolicies(callback?: (err: AWSError, data: SES.Types.ListIdentityPoliciesResponse) => void): Request<SES.Types.ListIdentityPoliciesResponse, AWSError>;
  /**
   * Lists the IP address filters associated with your Amazon Web Services account in the current Amazon Web Services Region. For information about managing IP address filters, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listReceiptFilters(params: SES.Types.ListReceiptFiltersRequest, callback?: (err: AWSError, data: SES.Types.ListReceiptFiltersResponse) => void): Request<SES.Types.ListReceiptFiltersResponse, AWSError>;
  /**
   * Lists the IP address filters associated with your Amazon Web Services account in the current Amazon Web Services Region. For information about managing IP address filters, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listReceiptFilters(callback?: (err: AWSError, data: SES.Types.ListReceiptFiltersResponse) => void): Request<SES.Types.ListReceiptFiltersResponse, AWSError>;
  /**
   * Lists the receipt rule sets that exist under your Amazon Web Services account in the current Amazon Web Services Region. If there are additional receipt rule sets to be retrieved, you receive a NextToken that you can provide to the next call to ListReceiptRuleSets to retrieve the additional entries. For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listReceiptRuleSets(params: SES.Types.ListReceiptRuleSetsRequest, callback?: (err: AWSError, data: SES.Types.ListReceiptRuleSetsResponse) => void): Request<SES.Types.ListReceiptRuleSetsResponse, AWSError>;
  /**
   * Lists the receipt rule sets that exist under your Amazon Web Services account in the current Amazon Web Services Region. If there are additional receipt rule sets to be retrieved, you receive a NextToken that you can provide to the next call to ListReceiptRuleSets to retrieve the additional entries. For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  listReceiptRuleSets(callback?: (err: AWSError, data: SES.Types.ListReceiptRuleSetsResponse) => void): Request<SES.Types.ListReceiptRuleSetsResponse, AWSError>;
  /**
   * Lists the email templates present in your Amazon SES account in the current Amazon Web Services Region. You can execute this operation no more than once per second.
   */
  listTemplates(params: SES.Types.ListTemplatesRequest, callback?: (err: AWSError, data: SES.Types.ListTemplatesResponse) => void): Request<SES.Types.ListTemplatesResponse, AWSError>;
  /**
   * Lists the email templates present in your Amazon SES account in the current Amazon Web Services Region. You can execute this operation no more than once per second.
   */
  listTemplates(callback?: (err: AWSError, data: SES.Types.ListTemplatesResponse) => void): Request<SES.Types.ListTemplatesResponse, AWSError>;
  /**
   * Deprecated. Use the ListIdentities operation to list the email addresses and domains associated with your account.
   */
  listVerifiedEmailAddresses(callback?: (err: AWSError, data: SES.Types.ListVerifiedEmailAddressesResponse) => void): Request<SES.Types.ListVerifiedEmailAddressesResponse, AWSError>;
  /**
   * Adds or updates the delivery options for a configuration set.
   */
  putConfigurationSetDeliveryOptions(params: SES.Types.PutConfigurationSetDeliveryOptionsRequest, callback?: (err: AWSError, data: SES.Types.PutConfigurationSetDeliveryOptionsResponse) => void): Request<SES.Types.PutConfigurationSetDeliveryOptionsResponse, AWSError>;
  /**
   * Adds or updates the delivery options for a configuration set.
   */
  putConfigurationSetDeliveryOptions(callback?: (err: AWSError, data: SES.Types.PutConfigurationSetDeliveryOptionsResponse) => void): Request<SES.Types.PutConfigurationSetDeliveryOptionsResponse, AWSError>;
  /**
   * Adds or updates a sending authorization policy for the specified identity (an email address or a domain).  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  putIdentityPolicy(params: SES.Types.PutIdentityPolicyRequest, callback?: (err: AWSError, data: SES.Types.PutIdentityPolicyResponse) => void): Request<SES.Types.PutIdentityPolicyResponse, AWSError>;
  /**
   * Adds or updates a sending authorization policy for the specified identity (an email address or a domain).  This operation is for the identity owner only. If you have not verified the identity, it returns an error.  Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  putIdentityPolicy(callback?: (err: AWSError, data: SES.Types.PutIdentityPolicyResponse) => void): Request<SES.Types.PutIdentityPolicyResponse, AWSError>;
  /**
   * Reorders the receipt rules within a receipt rule set.  All of the rules in the rule set must be represented in this request. That is, it is error if the reorder request doesn't explicitly position all of the rules.  For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  reorderReceiptRuleSet(params: SES.Types.ReorderReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.ReorderReceiptRuleSetResponse) => void): Request<SES.Types.ReorderReceiptRuleSetResponse, AWSError>;
  /**
   * Reorders the receipt rules within a receipt rule set.  All of the rules in the rule set must be represented in this request. That is, it is error if the reorder request doesn't explicitly position all of the rules.  For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  reorderReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.ReorderReceiptRuleSetResponse) => void): Request<SES.Types.ReorderReceiptRuleSetResponse, AWSError>;
  /**
   * Generates and sends a bounce message to the sender of an email you received through Amazon SES. You can only use this operation on an email up to 24 hours after you receive it.  You cannot use this operation to send generic bounces for mail that was not received by Amazon SES.  For information about receiving email through Amazon SES, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  sendBounce(params: SES.Types.SendBounceRequest, callback?: (err: AWSError, data: SES.Types.SendBounceResponse) => void): Request<SES.Types.SendBounceResponse, AWSError>;
  /**
   * Generates and sends a bounce message to the sender of an email you received through Amazon SES. You can only use this operation on an email up to 24 hours after you receive it.  You cannot use this operation to send generic bounces for mail that was not received by Amazon SES.  For information about receiving email through Amazon SES, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  sendBounce(callback?: (err: AWSError, data: SES.Types.SendBounceResponse) => void): Request<SES.Types.SendBounceResponse, AWSError>;
  /**
   * Composes an email message to multiple destinations. The message body is created using an email template. To send email using this operation, your call must meet the following requirements:   The call must refer to an existing email template. You can create email templates using CreateTemplate.   The message must be sent from a verified email address or domain.   If your account is still in the Amazon SES sandbox, you may send only to verified addresses or domains, or to email addresses associated with the Amazon SES Mailbox Simulator. For more information, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.    The maximum message size is 10 MB.   Each Destination parameter must include at least one recipient email address. The recipient address can be a To: address, a CC: address, or a BCC: address. If a recipient email address is invalid (that is, it is not in the format UserName@[SubDomain.]Domain.TopLevelDomain), the entire message is rejected, even if the message contains other recipients that are valid.   The message may not include more than 50 recipients, across the To:, CC: and BCC: fields. If you need to send an email message to a larger audience, you can divide your recipient list into groups of 50 or fewer, and then call the SendBulkTemplatedEmail operation several times to send the message to each group.   The number of destinations you can contact in a single call can be limited by your account's maximum sending rate.  
   */
  sendBulkTemplatedEmail(params: SES.Types.SendBulkTemplatedEmailRequest, callback?: (err: AWSError, data: SES.Types.SendBulkTemplatedEmailResponse) => void): Request<SES.Types.SendBulkTemplatedEmailResponse, AWSError>;
  /**
   * Composes an email message to multiple destinations. The message body is created using an email template. To send email using this operation, your call must meet the following requirements:   The call must refer to an existing email template. You can create email templates using CreateTemplate.   The message must be sent from a verified email address or domain.   If your account is still in the Amazon SES sandbox, you may send only to verified addresses or domains, or to email addresses associated with the Amazon SES Mailbox Simulator. For more information, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.    The maximum message size is 10 MB.   Each Destination parameter must include at least one recipient email address. The recipient address can be a To: address, a CC: address, or a BCC: address. If a recipient email address is invalid (that is, it is not in the format UserName@[SubDomain.]Domain.TopLevelDomain), the entire message is rejected, even if the message contains other recipients that are valid.   The message may not include more than 50 recipients, across the To:, CC: and BCC: fields. If you need to send an email message to a larger audience, you can divide your recipient list into groups of 50 or fewer, and then call the SendBulkTemplatedEmail operation several times to send the message to each group.   The number of destinations you can contact in a single call can be limited by your account's maximum sending rate.  
   */
  sendBulkTemplatedEmail(callback?: (err: AWSError, data: SES.Types.SendBulkTemplatedEmailResponse) => void): Request<SES.Types.SendBulkTemplatedEmailResponse, AWSError>;
  /**
   * Adds an email address to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. As a result of executing this operation, a customized verification email is sent to the specified address. To use this operation, you must first create a custom verification email template. For more information about creating and using custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  sendCustomVerificationEmail(params: SES.Types.SendCustomVerificationEmailRequest, callback?: (err: AWSError, data: SES.Types.SendCustomVerificationEmailResponse) => void): Request<SES.Types.SendCustomVerificationEmailResponse, AWSError>;
  /**
   * Adds an email address to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. As a result of executing this operation, a customized verification email is sent to the specified address. To use this operation, you must first create a custom verification email template. For more information about creating and using custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  sendCustomVerificationEmail(callback?: (err: AWSError, data: SES.Types.SendCustomVerificationEmailResponse) => void): Request<SES.Types.SendCustomVerificationEmailResponse, AWSError>;
  /**
   * Composes an email message and immediately queues it for sending. To send email using this operation, your message must meet the following requirements:   The message must be sent from a verified email address or domain. If you attempt to send email using a non-verified address or domain, the operation results in an "Email address not verified" error.    If your account is still in the Amazon SES sandbox, you may only send to verified addresses or domains, or to email addresses associated with the Amazon SES Mailbox Simulator. For more information, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.    The maximum message size is 10 MB.   The message must include at least one recipient email address. The recipient address can be a To: address, a CC: address, or a BCC: address. If a recipient email address is invalid (that is, it is not in the format UserName@[SubDomain.]Domain.TopLevelDomain), the entire message is rejected, even if the message contains other recipients that are valid.   The message may not include more than 50 recipients, across the To:, CC: and BCC: fields. If you need to send an email message to a larger audience, you can divide your recipient list into groups of 50 or fewer, and then call the SendEmail operation several times to send the message to each group.    For every message that you send, the total number of recipients (including each recipient in the To:, CC: and BCC: fields) is counted against the maximum number of emails you can send in a 24-hour period (your sending quota). For more information about sending quotas in Amazon SES, see Managing Your Amazon SES Sending Limits in the Amazon SES Developer Guide.  
   */
  sendEmail(params: SES.Types.SendEmailRequest, callback?: (err: AWSError, data: SES.Types.SendEmailResponse) => void): Request<SES.Types.SendEmailResponse, AWSError>;
  /**
   * Composes an email message and immediately queues it for sending. To send email using this operation, your message must meet the following requirements:   The message must be sent from a verified email address or domain. If you attempt to send email using a non-verified address or domain, the operation results in an "Email address not verified" error.    If your account is still in the Amazon SES sandbox, you may only send to verified addresses or domains, or to email addresses associated with the Amazon SES Mailbox Simulator. For more information, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.    The maximum message size is 10 MB.   The message must include at least one recipient email address. The recipient address can be a To: address, a CC: address, or a BCC: address. If a recipient email address is invalid (that is, it is not in the format UserName@[SubDomain.]Domain.TopLevelDomain), the entire message is rejected, even if the message contains other recipients that are valid.   The message may not include more than 50 recipients, across the To:, CC: and BCC: fields. If you need to send an email message to a larger audience, you can divide your recipient list into groups of 50 or fewer, and then call the SendEmail operation several times to send the message to each group.    For every message that you send, the total number of recipients (including each recipient in the To:, CC: and BCC: fields) is counted against the maximum number of emails you can send in a 24-hour period (your sending quota). For more information about sending quotas in Amazon SES, see Managing Your Amazon SES Sending Limits in the Amazon SES Developer Guide.  
   */
  sendEmail(callback?: (err: AWSError, data: SES.Types.SendEmailResponse) => void): Request<SES.Types.SendEmailResponse, AWSError>;
  /**
   * Composes an email message and immediately queues it for sending. This operation is more flexible than the SendEmail operation. When you use the SendRawEmail operation, you can specify the headers of the message as well as its content. This flexibility is useful, for example, when you need to send a multipart MIME email (such a message that contains both a text and an HTML version). You can also use this operation to send messages that include attachments. The SendRawEmail operation has the following requirements:   You can only send email from verified email addresses or domains. If you try to send email from an address that isn't verified, the operation results in an "Email address not verified" error.   If your account is still in the Amazon SES sandbox, you can only send email to other verified addresses in your account, or to addresses that are associated with the Amazon SES mailbox simulator.   The maximum message size, including attachments, is 10 MB.   Each message has to include at least one recipient address. A recipient address includes any address on the To:, CC:, or BCC: lines.   If you send a single message to more than one recipient address, and one of the recipient addresses isn't in a valid format (that is, it's not in the format UserName@[SubDomain.]Domain.TopLevelDomain), Amazon SES rejects the entire message, even if the other addresses are valid.   Each message can include up to 50 recipient addresses across the To:, CC:, or BCC: lines. If you need to send a single message to more than 50 recipients, you have to split the list of recipient addresses into groups of less than 50 recipients, and send separate messages to each group.   Amazon SES allows you to specify 8-bit Content-Transfer-Encoding for MIME message parts. However, if Amazon SES has to modify the contents of your message (for example, if you use open and click tracking), 8-bit content isn't preserved. For this reason, we highly recommend that you encode all content that isn't 7-bit ASCII. For more information, see MIME Encoding in the Amazon SES Developer Guide.   Additionally, keep the following considerations in mind when using the SendRawEmail operation:   Although you can customize the message headers when using the SendRawEmail operation, Amazon SES automatically applies its own Message-ID and Date headers; if you passed these headers when creating the message, they are overwritten by the values that Amazon SES provides.   If you are using sending authorization to send on behalf of another user, SendRawEmail enables you to specify the cross-account identity for the email's Source, From, and Return-Path parameters in one of two ways: you can pass optional parameters SourceArn, FromArn, and/or ReturnPathArn, or you can include the following X-headers in the header of your raw email:    X-SES-SOURCE-ARN     X-SES-FROM-ARN     X-SES-RETURN-PATH-ARN     Don't include these X-headers in the DKIM signature. Amazon SES removes these before it sends the email.  If you only specify the SourceIdentityArn parameter, Amazon SES sets the From and Return-Path addresses to the same identity that you specified. For more information about sending authorization, see the Using Sending Authorization with Amazon SES in the Amazon SES Developer Guide.    For every message that you send, the total number of recipients (including each recipient in the To:, CC: and BCC: fields) is counted against the maximum number of emails you can send in a 24-hour period (your sending quota). For more information about sending quotas in Amazon SES, see Managing Your Amazon SES Sending Limits in the Amazon SES Developer Guide.   
   */
  sendRawEmail(params: SES.Types.SendRawEmailRequest, callback?: (err: AWSError, data: SES.Types.SendRawEmailResponse) => void): Request<SES.Types.SendRawEmailResponse, AWSError>;
  /**
   * Composes an email message and immediately queues it for sending. This operation is more flexible than the SendEmail operation. When you use the SendRawEmail operation, you can specify the headers of the message as well as its content. This flexibility is useful, for example, when you need to send a multipart MIME email (such a message that contains both a text and an HTML version). You can also use this operation to send messages that include attachments. The SendRawEmail operation has the following requirements:   You can only send email from verified email addresses or domains. If you try to send email from an address that isn't verified, the operation results in an "Email address not verified" error.   If your account is still in the Amazon SES sandbox, you can only send email to other verified addresses in your account, or to addresses that are associated with the Amazon SES mailbox simulator.   The maximum message size, including attachments, is 10 MB.   Each message has to include at least one recipient address. A recipient address includes any address on the To:, CC:, or BCC: lines.   If you send a single message to more than one recipient address, and one of the recipient addresses isn't in a valid format (that is, it's not in the format UserName@[SubDomain.]Domain.TopLevelDomain), Amazon SES rejects the entire message, even if the other addresses are valid.   Each message can include up to 50 recipient addresses across the To:, CC:, or BCC: lines. If you need to send a single message to more than 50 recipients, you have to split the list of recipient addresses into groups of less than 50 recipients, and send separate messages to each group.   Amazon SES allows you to specify 8-bit Content-Transfer-Encoding for MIME message parts. However, if Amazon SES has to modify the contents of your message (for example, if you use open and click tracking), 8-bit content isn't preserved. For this reason, we highly recommend that you encode all content that isn't 7-bit ASCII. For more information, see MIME Encoding in the Amazon SES Developer Guide.   Additionally, keep the following considerations in mind when using the SendRawEmail operation:   Although you can customize the message headers when using the SendRawEmail operation, Amazon SES automatically applies its own Message-ID and Date headers; if you passed these headers when creating the message, they are overwritten by the values that Amazon SES provides.   If you are using sending authorization to send on behalf of another user, SendRawEmail enables you to specify the cross-account identity for the email's Source, From, and Return-Path parameters in one of two ways: you can pass optional parameters SourceArn, FromArn, and/or ReturnPathArn, or you can include the following X-headers in the header of your raw email:    X-SES-SOURCE-ARN     X-SES-FROM-ARN     X-SES-RETURN-PATH-ARN     Don't include these X-headers in the DKIM signature. Amazon SES removes these before it sends the email.  If you only specify the SourceIdentityArn parameter, Amazon SES sets the From and Return-Path addresses to the same identity that you specified. For more information about sending authorization, see the Using Sending Authorization with Amazon SES in the Amazon SES Developer Guide.    For every message that you send, the total number of recipients (including each recipient in the To:, CC: and BCC: fields) is counted against the maximum number of emails you can send in a 24-hour period (your sending quota). For more information about sending quotas in Amazon SES, see Managing Your Amazon SES Sending Limits in the Amazon SES Developer Guide.   
   */
  sendRawEmail(callback?: (err: AWSError, data: SES.Types.SendRawEmailResponse) => void): Request<SES.Types.SendRawEmailResponse, AWSError>;
  /**
   * Composes an email message using an email template and immediately queues it for sending. To send email using this operation, your call must meet the following requirements:   The call must refer to an existing email template. You can create email templates using the CreateTemplate operation.   The message must be sent from a verified email address or domain.   If your account is still in the Amazon SES sandbox, you may only send to verified addresses or domains, or to email addresses associated with the Amazon SES Mailbox Simulator. For more information, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.    The maximum message size is 10 MB.   Calls to the SendTemplatedEmail operation may only include one Destination parameter. A destination is a set of recipients that receives the same version of the email. The Destination parameter can include up to 50 recipients, across the To:, CC: and BCC: fields.   The Destination parameter must include at least one recipient email address. The recipient address can be a To: address, a CC: address, or a BCC: address. If a recipient email address is invalid (that is, it is not in the format UserName@[SubDomain.]Domain.TopLevelDomain), the entire message is rejected, even if the message contains other recipients that are valid.    If your call to the SendTemplatedEmail operation includes all of the required parameters, Amazon SES accepts it and returns a Message ID. However, if Amazon SES can't render the email because the template contains errors, it doesn't send the email. Additionally, because it already accepted the message, Amazon SES doesn't return a message stating that it was unable to send the email. For these reasons, we highly recommend that you set up Amazon SES to send you notifications when Rendering Failure events occur. For more information, see Sending Personalized Email Using the Amazon SES API in the Amazon Simple Email Service Developer Guide. 
   */
  sendTemplatedEmail(params: SES.Types.SendTemplatedEmailRequest, callback?: (err: AWSError, data: SES.Types.SendTemplatedEmailResponse) => void): Request<SES.Types.SendTemplatedEmailResponse, AWSError>;
  /**
   * Composes an email message using an email template and immediately queues it for sending. To send email using this operation, your call must meet the following requirements:   The call must refer to an existing email template. You can create email templates using the CreateTemplate operation.   The message must be sent from a verified email address or domain.   If your account is still in the Amazon SES sandbox, you may only send to verified addresses or domains, or to email addresses associated with the Amazon SES Mailbox Simulator. For more information, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.    The maximum message size is 10 MB.   Calls to the SendTemplatedEmail operation may only include one Destination parameter. A destination is a set of recipients that receives the same version of the email. The Destination parameter can include up to 50 recipients, across the To:, CC: and BCC: fields.   The Destination parameter must include at least one recipient email address. The recipient address can be a To: address, a CC: address, or a BCC: address. If a recipient email address is invalid (that is, it is not in the format UserName@[SubDomain.]Domain.TopLevelDomain), the entire message is rejected, even if the message contains other recipients that are valid.    If your call to the SendTemplatedEmail operation includes all of the required parameters, Amazon SES accepts it and returns a Message ID. However, if Amazon SES can't render the email because the template contains errors, it doesn't send the email. Additionally, because it already accepted the message, Amazon SES doesn't return a message stating that it was unable to send the email. For these reasons, we highly recommend that you set up Amazon SES to send you notifications when Rendering Failure events occur. For more information, see Sending Personalized Email Using the Amazon SES API in the Amazon Simple Email Service Developer Guide. 
   */
  sendTemplatedEmail(callback?: (err: AWSError, data: SES.Types.SendTemplatedEmailResponse) => void): Request<SES.Types.SendTemplatedEmailResponse, AWSError>;
  /**
   * Sets the specified receipt rule set as the active receipt rule set.  To disable your email-receiving through Amazon SES completely, you can call this operation with RuleSetName set to null.  For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  setActiveReceiptRuleSet(params: SES.Types.SetActiveReceiptRuleSetRequest, callback?: (err: AWSError, data: SES.Types.SetActiveReceiptRuleSetResponse) => void): Request<SES.Types.SetActiveReceiptRuleSetResponse, AWSError>;
  /**
   * Sets the specified receipt rule set as the active receipt rule set.  To disable your email-receiving through Amazon SES completely, you can call this operation with RuleSetName set to null.  For information about managing receipt rule sets, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  setActiveReceiptRuleSet(callback?: (err: AWSError, data: SES.Types.SetActiveReceiptRuleSetResponse) => void): Request<SES.Types.SetActiveReceiptRuleSetResponse, AWSError>;
  /**
   * Enables or disables Easy DKIM signing of email sent from an identity. If Easy DKIM signing is enabled for a domain, then Amazon SES uses DKIM to sign all email that it sends from addresses on that domain. If Easy DKIM signing is enabled for an email address, then Amazon SES uses DKIM to sign all email it sends from that address.  For email addresses (for example, <EMAIL>), you can only enable DKIM signing if the corresponding domain (in this case, example.com) has been set up to use Easy DKIM.  You can enable DKIM signing for an identity at any time after you start the verification process for the identity, even if the verification process isn't complete.  You can execute this operation no more than once per second. For more information about Easy DKIM signing, go to the Amazon SES Developer Guide.
   */
  setIdentityDkimEnabled(params: SES.Types.SetIdentityDkimEnabledRequest, callback?: (err: AWSError, data: SES.Types.SetIdentityDkimEnabledResponse) => void): Request<SES.Types.SetIdentityDkimEnabledResponse, AWSError>;
  /**
   * Enables or disables Easy DKIM signing of email sent from an identity. If Easy DKIM signing is enabled for a domain, then Amazon SES uses DKIM to sign all email that it sends from addresses on that domain. If Easy DKIM signing is enabled for an email address, then Amazon SES uses DKIM to sign all email it sends from that address.  For email addresses (for example, <EMAIL>), you can only enable DKIM signing if the corresponding domain (in this case, example.com) has been set up to use Easy DKIM.  You can enable DKIM signing for an identity at any time after you start the verification process for the identity, even if the verification process isn't complete.  You can execute this operation no more than once per second. For more information about Easy DKIM signing, go to the Amazon SES Developer Guide.
   */
  setIdentityDkimEnabled(callback?: (err: AWSError, data: SES.Types.SetIdentityDkimEnabledResponse) => void): Request<SES.Types.SetIdentityDkimEnabledResponse, AWSError>;
  /**
   * Given an identity (an email address or a domain), enables or disables whether Amazon SES forwards bounce and complaint notifications as email. Feedback forwarding can only be disabled when Amazon Simple Notification Service (Amazon SNS) topics are specified for both bounces and complaints.  Feedback forwarding does not apply to delivery notifications. Delivery notifications are only available through Amazon SNS.  You can execute this operation no more than once per second. For more information about using notifications with Amazon SES, see the Amazon SES Developer Guide.
   */
  setIdentityFeedbackForwardingEnabled(params: SES.Types.SetIdentityFeedbackForwardingEnabledRequest, callback?: (err: AWSError, data: SES.Types.SetIdentityFeedbackForwardingEnabledResponse) => void): Request<SES.Types.SetIdentityFeedbackForwardingEnabledResponse, AWSError>;
  /**
   * Given an identity (an email address or a domain), enables or disables whether Amazon SES forwards bounce and complaint notifications as email. Feedback forwarding can only be disabled when Amazon Simple Notification Service (Amazon SNS) topics are specified for both bounces and complaints.  Feedback forwarding does not apply to delivery notifications. Delivery notifications are only available through Amazon SNS.  You can execute this operation no more than once per second. For more information about using notifications with Amazon SES, see the Amazon SES Developer Guide.
   */
  setIdentityFeedbackForwardingEnabled(callback?: (err: AWSError, data: SES.Types.SetIdentityFeedbackForwardingEnabledResponse) => void): Request<SES.Types.SetIdentityFeedbackForwardingEnabledResponse, AWSError>;
  /**
   * Given an identity (an email address or a domain), sets whether Amazon SES includes the original email headers in the Amazon Simple Notification Service (Amazon SNS) notifications of a specified type. You can execute this operation no more than once per second. For more information about using notifications with Amazon SES, see the Amazon SES Developer Guide.
   */
  setIdentityHeadersInNotificationsEnabled(params: SES.Types.SetIdentityHeadersInNotificationsEnabledRequest, callback?: (err: AWSError, data: SES.Types.SetIdentityHeadersInNotificationsEnabledResponse) => void): Request<SES.Types.SetIdentityHeadersInNotificationsEnabledResponse, AWSError>;
  /**
   * Given an identity (an email address or a domain), sets whether Amazon SES includes the original email headers in the Amazon Simple Notification Service (Amazon SNS) notifications of a specified type. You can execute this operation no more than once per second. For more information about using notifications with Amazon SES, see the Amazon SES Developer Guide.
   */
  setIdentityHeadersInNotificationsEnabled(callback?: (err: AWSError, data: SES.Types.SetIdentityHeadersInNotificationsEnabledResponse) => void): Request<SES.Types.SetIdentityHeadersInNotificationsEnabledResponse, AWSError>;
  /**
   * Enables or disables the custom MAIL FROM domain setup for a verified identity (an email address or a domain).  To send emails using the specified MAIL FROM domain, you must add an MX record to your MAIL FROM domain's DNS settings. To ensure that your emails pass Sender Policy Framework (SPF) checks, you must also add or update an SPF record. For more information, see the Amazon SES Developer Guide.  You can execute this operation no more than once per second.
   */
  setIdentityMailFromDomain(params: SES.Types.SetIdentityMailFromDomainRequest, callback?: (err: AWSError, data: SES.Types.SetIdentityMailFromDomainResponse) => void): Request<SES.Types.SetIdentityMailFromDomainResponse, AWSError>;
  /**
   * Enables or disables the custom MAIL FROM domain setup for a verified identity (an email address or a domain).  To send emails using the specified MAIL FROM domain, you must add an MX record to your MAIL FROM domain's DNS settings. To ensure that your emails pass Sender Policy Framework (SPF) checks, you must also add or update an SPF record. For more information, see the Amazon SES Developer Guide.  You can execute this operation no more than once per second.
   */
  setIdentityMailFromDomain(callback?: (err: AWSError, data: SES.Types.SetIdentityMailFromDomainResponse) => void): Request<SES.Types.SetIdentityMailFromDomainResponse, AWSError>;
  /**
   * Sets an Amazon Simple Notification Service (Amazon SNS) topic to use when delivering notifications. When you use this operation, you specify a verified identity, such as an email address or domain. When you send an email that uses the chosen identity in the Source field, Amazon SES sends notifications to the topic you specified. You can send bounce, complaint, or delivery notifications (or any combination of the three) to the Amazon SNS topic that you specify. You can execute this operation no more than once per second. For more information about feedback notification, see the Amazon SES Developer Guide.
   */
  setIdentityNotificationTopic(params: SES.Types.SetIdentityNotificationTopicRequest, callback?: (err: AWSError, data: SES.Types.SetIdentityNotificationTopicResponse) => void): Request<SES.Types.SetIdentityNotificationTopicResponse, AWSError>;
  /**
   * Sets an Amazon Simple Notification Service (Amazon SNS) topic to use when delivering notifications. When you use this operation, you specify a verified identity, such as an email address or domain. When you send an email that uses the chosen identity in the Source field, Amazon SES sends notifications to the topic you specified. You can send bounce, complaint, or delivery notifications (or any combination of the three) to the Amazon SNS topic that you specify. You can execute this operation no more than once per second. For more information about feedback notification, see the Amazon SES Developer Guide.
   */
  setIdentityNotificationTopic(callback?: (err: AWSError, data: SES.Types.SetIdentityNotificationTopicResponse) => void): Request<SES.Types.SetIdentityNotificationTopicResponse, AWSError>;
  /**
   * Sets the position of the specified receipt rule in the receipt rule set. For information about managing receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  setReceiptRulePosition(params: SES.Types.SetReceiptRulePositionRequest, callback?: (err: AWSError, data: SES.Types.SetReceiptRulePositionResponse) => void): Request<SES.Types.SetReceiptRulePositionResponse, AWSError>;
  /**
   * Sets the position of the specified receipt rule in the receipt rule set. For information about managing receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  setReceiptRulePosition(callback?: (err: AWSError, data: SES.Types.SetReceiptRulePositionResponse) => void): Request<SES.Types.SetReceiptRulePositionResponse, AWSError>;
  /**
   * Creates a preview of the MIME content of an email when provided with a template and a set of replacement data. You can execute this operation no more than once per second.
   */
  testRenderTemplate(params: SES.Types.TestRenderTemplateRequest, callback?: (err: AWSError, data: SES.Types.TestRenderTemplateResponse) => void): Request<SES.Types.TestRenderTemplateResponse, AWSError>;
  /**
   * Creates a preview of the MIME content of an email when provided with a template and a set of replacement data. You can execute this operation no more than once per second.
   */
  testRenderTemplate(callback?: (err: AWSError, data: SES.Types.TestRenderTemplateResponse) => void): Request<SES.Types.TestRenderTemplateResponse, AWSError>;
  /**
   * Enables or disables email sending across your entire Amazon SES account in the current Amazon Web Services Region. You can use this operation in conjunction with Amazon CloudWatch alarms to temporarily pause email sending across your Amazon SES account in a given Amazon Web Services Region when reputation metrics (such as your bounce or complaint rates) reach certain thresholds. You can execute this operation no more than once per second.
   */
  updateAccountSendingEnabled(params: SES.Types.UpdateAccountSendingEnabledRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Enables or disables email sending across your entire Amazon SES account in the current Amazon Web Services Region. You can use this operation in conjunction with Amazon CloudWatch alarms to temporarily pause email sending across your Amazon SES account in a given Amazon Web Services Region when reputation metrics (such as your bounce or complaint rates) reach certain thresholds. You can execute this operation no more than once per second.
   */
  updateAccountSendingEnabled(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Updates the event destination of a configuration set. Event destinations are associated with configuration sets, which enable you to publish email sending events to Amazon CloudWatch, Amazon Kinesis Firehose, or Amazon Simple Notification Service (Amazon SNS). For information about using configuration sets, see Monitoring Your Amazon SES Sending Activity in the Amazon SES Developer Guide.   When you create or update an event destination, you must provide one, and only one, destination. The destination can be Amazon CloudWatch, Amazon Kinesis Firehose, or Amazon Simple Notification Service (Amazon SNS).  You can execute this operation no more than once per second.
   */
  updateConfigurationSetEventDestination(params: SES.Types.UpdateConfigurationSetEventDestinationRequest, callback?: (err: AWSError, data: SES.Types.UpdateConfigurationSetEventDestinationResponse) => void): Request<SES.Types.UpdateConfigurationSetEventDestinationResponse, AWSError>;
  /**
   * Updates the event destination of a configuration set. Event destinations are associated with configuration sets, which enable you to publish email sending events to Amazon CloudWatch, Amazon Kinesis Firehose, or Amazon Simple Notification Service (Amazon SNS). For information about using configuration sets, see Monitoring Your Amazon SES Sending Activity in the Amazon SES Developer Guide.   When you create or update an event destination, you must provide one, and only one, destination. The destination can be Amazon CloudWatch, Amazon Kinesis Firehose, or Amazon Simple Notification Service (Amazon SNS).  You can execute this operation no more than once per second.
   */
  updateConfigurationSetEventDestination(callback?: (err: AWSError, data: SES.Types.UpdateConfigurationSetEventDestinationResponse) => void): Request<SES.Types.UpdateConfigurationSetEventDestinationResponse, AWSError>;
  /**
   * Enables or disables the publishing of reputation metrics for emails sent using a specific configuration set in a given Amazon Web Services Region. Reputation metrics include bounce and complaint rates. These metrics are published to Amazon CloudWatch. By using CloudWatch, you can create alarms when bounce or complaint rates exceed certain thresholds. You can execute this operation no more than once per second.
   */
  updateConfigurationSetReputationMetricsEnabled(params: SES.Types.UpdateConfigurationSetReputationMetricsEnabledRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Enables or disables the publishing of reputation metrics for emails sent using a specific configuration set in a given Amazon Web Services Region. Reputation metrics include bounce and complaint rates. These metrics are published to Amazon CloudWatch. By using CloudWatch, you can create alarms when bounce or complaint rates exceed certain thresholds. You can execute this operation no more than once per second.
   */
  updateConfigurationSetReputationMetricsEnabled(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Enables or disables email sending for messages sent using a specific configuration set in a given Amazon Web Services Region. You can use this operation in conjunction with Amazon CloudWatch alarms to temporarily pause email sending for a configuration set when the reputation metrics for that configuration set (such as your bounce on complaint rate) exceed certain thresholds. You can execute this operation no more than once per second.
   */
  updateConfigurationSetSendingEnabled(params: SES.Types.UpdateConfigurationSetSendingEnabledRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Enables or disables email sending for messages sent using a specific configuration set in a given Amazon Web Services Region. You can use this operation in conjunction with Amazon CloudWatch alarms to temporarily pause email sending for a configuration set when the reputation metrics for that configuration set (such as your bounce on complaint rate) exceed certain thresholds. You can execute this operation no more than once per second.
   */
  updateConfigurationSetSendingEnabled(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Modifies an association between a configuration set and a custom domain for open and click event tracking.  By default, images and links used for tracking open and click events are hosted on domains operated by Amazon SES. You can configure a subdomain of your own to handle these events. For information about using custom domains, see the Amazon SES Developer Guide.
   */
  updateConfigurationSetTrackingOptions(params: SES.Types.UpdateConfigurationSetTrackingOptionsRequest, callback?: (err: AWSError, data: SES.Types.UpdateConfigurationSetTrackingOptionsResponse) => void): Request<SES.Types.UpdateConfigurationSetTrackingOptionsResponse, AWSError>;
  /**
   * Modifies an association between a configuration set and a custom domain for open and click event tracking.  By default, images and links used for tracking open and click events are hosted on domains operated by Amazon SES. You can configure a subdomain of your own to handle these events. For information about using custom domains, see the Amazon SES Developer Guide.
   */
  updateConfigurationSetTrackingOptions(callback?: (err: AWSError, data: SES.Types.UpdateConfigurationSetTrackingOptionsResponse) => void): Request<SES.Types.UpdateConfigurationSetTrackingOptionsResponse, AWSError>;
  /**
   * Updates an existing custom verification email template. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  updateCustomVerificationEmailTemplate(params: SES.Types.UpdateCustomVerificationEmailTemplateRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Updates an existing custom verification email template. For more information about custom verification email templates, see Using Custom Verification Email Templates in the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  updateCustomVerificationEmailTemplate(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Updates a receipt rule. For information about managing receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  updateReceiptRule(params: SES.Types.UpdateReceiptRuleRequest, callback?: (err: AWSError, data: SES.Types.UpdateReceiptRuleResponse) => void): Request<SES.Types.UpdateReceiptRuleResponse, AWSError>;
  /**
   * Updates a receipt rule. For information about managing receipt rules, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  updateReceiptRule(callback?: (err: AWSError, data: SES.Types.UpdateReceiptRuleResponse) => void): Request<SES.Types.UpdateReceiptRuleResponse, AWSError>;
  /**
   * Updates an email template. Email templates enable you to send personalized email to one or more destinations in a single operation. For more information, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  updateTemplate(params: SES.Types.UpdateTemplateRequest, callback?: (err: AWSError, data: SES.Types.UpdateTemplateResponse) => void): Request<SES.Types.UpdateTemplateResponse, AWSError>;
  /**
   * Updates an email template. Email templates enable you to send personalized email to one or more destinations in a single operation. For more information, see the Amazon SES Developer Guide. You can execute this operation no more than once per second.
   */
  updateTemplate(callback?: (err: AWSError, data: SES.Types.UpdateTemplateResponse) => void): Request<SES.Types.UpdateTemplateResponse, AWSError>;
  /**
   * Returns a set of DKIM tokens for a domain identity.  When you execute the VerifyDomainDkim operation, the domain that you specify is added to the list of identities that are associated with your account. This is true even if you haven't already associated the domain with your account by using the VerifyDomainIdentity operation. However, you can't send email from the domain until you either successfully verify it or you successfully set up DKIM for it.  You use the tokens that are generated by this operation to create CNAME records. When Amazon SES detects that you've added these records to the DNS configuration for a domain, you can start sending email from that domain. You can start sending email even if you haven't added the TXT record provided by the VerifyDomainIdentity operation to the DNS configuration for your domain. All email that you send from the domain is authenticated using DKIM. To create the CNAME records for DKIM authentication, use the following values:    Name: token._domainkey.example.com     Type: CNAME    Value: token.dkim.amazonses.com   In the preceding example, replace token with one of the tokens that are generated when you execute this operation. Replace example.com with your domain. Repeat this process for each token that's generated by this operation. You can execute this operation no more than once per second.
   */
  verifyDomainDkim(params: SES.Types.VerifyDomainDkimRequest, callback?: (err: AWSError, data: SES.Types.VerifyDomainDkimResponse) => void): Request<SES.Types.VerifyDomainDkimResponse, AWSError>;
  /**
   * Returns a set of DKIM tokens for a domain identity.  When you execute the VerifyDomainDkim operation, the domain that you specify is added to the list of identities that are associated with your account. This is true even if you haven't already associated the domain with your account by using the VerifyDomainIdentity operation. However, you can't send email from the domain until you either successfully verify it or you successfully set up DKIM for it.  You use the tokens that are generated by this operation to create CNAME records. When Amazon SES detects that you've added these records to the DNS configuration for a domain, you can start sending email from that domain. You can start sending email even if you haven't added the TXT record provided by the VerifyDomainIdentity operation to the DNS configuration for your domain. All email that you send from the domain is authenticated using DKIM. To create the CNAME records for DKIM authentication, use the following values:    Name: token._domainkey.example.com     Type: CNAME    Value: token.dkim.amazonses.com   In the preceding example, replace token with one of the tokens that are generated when you execute this operation. Replace example.com with your domain. Repeat this process for each token that's generated by this operation. You can execute this operation no more than once per second.
   */
  verifyDomainDkim(callback?: (err: AWSError, data: SES.Types.VerifyDomainDkimResponse) => void): Request<SES.Types.VerifyDomainDkimResponse, AWSError>;
  /**
   * Adds a domain to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. For more information about verifying domains, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.  You can execute this operation no more than once per second.
   */
  verifyDomainIdentity(params: SES.Types.VerifyDomainIdentityRequest, callback?: (err: AWSError, data: SES.Types.VerifyDomainIdentityResponse) => void): Request<SES.Types.VerifyDomainIdentityResponse, AWSError>;
  /**
   * Adds a domain to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. For more information about verifying domains, see Verifying Email Addresses and Domains in the Amazon SES Developer Guide.  You can execute this operation no more than once per second.
   */
  verifyDomainIdentity(callback?: (err: AWSError, data: SES.Types.VerifyDomainIdentityResponse) => void): Request<SES.Types.VerifyDomainIdentityResponse, AWSError>;
  /**
   * Deprecated. Use the VerifyEmailIdentity operation to verify a new email address.
   */
  verifyEmailAddress(params: SES.Types.VerifyEmailAddressRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deprecated. Use the VerifyEmailIdentity operation to verify a new email address.
   */
  verifyEmailAddress(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Adds an email address to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. As a result of executing this operation, a verification email is sent to the specified address. You can execute this operation no more than once per second.
   */
  verifyEmailIdentity(params: SES.Types.VerifyEmailIdentityRequest, callback?: (err: AWSError, data: SES.Types.VerifyEmailIdentityResponse) => void): Request<SES.Types.VerifyEmailIdentityResponse, AWSError>;
  /**
   * Adds an email address to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. As a result of executing this operation, a verification email is sent to the specified address. You can execute this operation no more than once per second.
   */
  verifyEmailIdentity(callback?: (err: AWSError, data: SES.Types.VerifyEmailIdentityResponse) => void): Request<SES.Types.VerifyEmailIdentityResponse, AWSError>;
  /**
   * Waits for the identityExists state by periodically calling the underlying SES.getIdentityVerificationAttributesoperation every 3 seconds (at most 20 times).
   */
  waitFor(state: "identityExists", params: SES.Types.GetIdentityVerificationAttributesRequest & {$waiter?: WaiterConfiguration}, callback?: (err: AWSError, data: SES.Types.GetIdentityVerificationAttributesResponse) => void): Request<SES.Types.GetIdentityVerificationAttributesResponse, AWSError>;
  /**
   * Waits for the identityExists state by periodically calling the underlying SES.getIdentityVerificationAttributesoperation every 3 seconds (at most 20 times).
   */
  waitFor(state: "identityExists", callback?: (err: AWSError, data: SES.Types.GetIdentityVerificationAttributesResponse) => void): Request<SES.Types.GetIdentityVerificationAttributesResponse, AWSError>;
}
declare namespace SES {
  export interface AddHeaderAction {
    /**
     * The name of the header to add to the incoming message. The name must contain at least one character, and can contain up to 50 characters. It consists of alphanumeric (a–z, A–Z, 0–9) characters and dashes.
     */
    HeaderName: HeaderName;
    /**
     * The content to include in the header. This value can contain up to 2048 characters. It can't contain newline (\n) or carriage return (\r) characters.
     */
    HeaderValue: HeaderValue;
  }
  export type Address = string;
  export type AddressList = Address[];
  export type AmazonResourceName = string;
  export type ArrivalDate = Date;
  export type BehaviorOnMXFailure = "UseDefaultValue"|"RejectMessage"|string;
  export interface Body {
    /**
     * The content of the message, in text format. Use this for text-based email clients, or clients on high-latency networks (such as mobile devices).
     */
    Text?: Content;
    /**
     * The content of the message, in HTML format. Use this for email clients that can process HTML. You can include clickable links, formatted text, and much more in an HTML message.
     */
    Html?: Content;
  }
  export interface BounceAction {
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic to notify when the bounce action is taken. You can find the ARN of a topic by using the ListTopics operation in Amazon SNS. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicArn?: AmazonResourceName;
    /**
     * The SMTP reply code, as defined by RFC 5321.
     */
    SmtpReplyCode: BounceSmtpReplyCode;
    /**
     * The SMTP enhanced status code, as defined by RFC 3463.
     */
    StatusCode?: BounceStatusCode;
    /**
     * Human-readable text to include in the bounce message.
     */
    Message: BounceMessage;
    /**
     * The email address of the sender of the bounced email. This is the address from which the bounce message is sent.
     */
    Sender: Address;
  }
  export type BounceMessage = string;
  export type BounceSmtpReplyCode = string;
  export type BounceStatusCode = string;
  export type BounceType = "DoesNotExist"|"MessageTooLarge"|"ExceededQuota"|"ContentRejected"|"Undefined"|"TemporaryFailure"|string;
  export interface BouncedRecipientInfo {
    /**
     * The email address of the recipient of the bounced email.
     */
    Recipient: Address;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to receive email for the recipient of the bounced email. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    RecipientArn?: AmazonResourceName;
    /**
     * The reason for the bounce. You must provide either this parameter or RecipientDsnFields.
     */
    BounceType?: BounceType;
    /**
     * Recipient-related DSN fields, most of which would normally be filled in automatically when provided with a BounceType. You must provide either this parameter or BounceType.
     */
    RecipientDsnFields?: RecipientDsnFields;
  }
  export type BouncedRecipientInfoList = BouncedRecipientInfo[];
  export interface BulkEmailDestination {
    Destination: Destination;
    /**
     * A list of tags, in the form of name/value pairs, to apply to an email that you send using SendBulkTemplatedEmail. Tags correspond to characteristics of the email that you define, so that you can publish email sending events.
     */
    ReplacementTags?: MessageTagList;
    /**
     * A list of replacement values to apply to the template. This parameter is a JSON object, typically consisting of key-value pairs in which the keys correspond to replacement tags in the email template.
     */
    ReplacementTemplateData?: TemplateData;
  }
  export type BulkEmailDestinationList = BulkEmailDestination[];
  export interface BulkEmailDestinationStatus {
    /**
     * The status of a message sent using the SendBulkTemplatedEmail operation. Possible values for this parameter include:    Success: Amazon SES accepted the message, and attempts to deliver it to the recipients.    MessageRejected: The message was rejected because it contained a virus.    MailFromDomainNotVerified: The sender's email address or domain was not verified.    ConfigurationSetDoesNotExist: The configuration set you specified does not exist.    TemplateDoesNotExist: The template you specified does not exist.    AccountSuspended: Your account has been shut down because of issues related to your email sending practices.    AccountThrottled: The number of emails you can send has been reduced because your account has exceeded its allocated sending limit.    AccountDailyQuotaExceeded: You have reached or exceeded the maximum number of emails you can send from your account in a 24-hour period.    InvalidSendingPoolName: The configuration set you specified refers to an IP pool that does not exist.    AccountSendingPaused: Email sending for the Amazon SES account was disabled using the UpdateAccountSendingEnabled operation.    ConfigurationSetSendingPaused: Email sending for this configuration set was disabled using the UpdateConfigurationSetSendingEnabled operation.    InvalidParameterValue: One or more of the parameters you specified when calling this operation was invalid. See the error message for additional information.    TransientFailure: Amazon SES was unable to process your request because of a temporary issue.    Failed: Amazon SES was unable to process your request. See the error message for additional information.  
     */
    Status?: BulkEmailStatus;
    /**
     * A description of an error that prevented a message being sent using the SendBulkTemplatedEmail operation.
     */
    Error?: Error;
    /**
     * The unique message identifier returned from the SendBulkTemplatedEmail operation.
     */
    MessageId?: MessageId;
  }
  export type BulkEmailDestinationStatusList = BulkEmailDestinationStatus[];
  export type BulkEmailStatus = "Success"|"MessageRejected"|"MailFromDomainNotVerified"|"ConfigurationSetDoesNotExist"|"TemplateDoesNotExist"|"AccountSuspended"|"AccountThrottled"|"AccountDailyQuotaExceeded"|"InvalidSendingPoolName"|"AccountSendingPaused"|"ConfigurationSetSendingPaused"|"InvalidParameterValue"|"TransientFailure"|"Failed"|string;
  export type Charset = string;
  export type Cidr = string;
  export interface CloneReceiptRuleSetRequest {
    /**
     * The name of the rule set to create. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Start and end with a letter or number.   Contain 64 characters or fewer.  
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * The name of the rule set to clone.
     */
    OriginalRuleSetName: ReceiptRuleSetName;
  }
  export interface CloneReceiptRuleSetResponse {
  }
  export interface CloudWatchDestination {
    /**
     * A list of dimensions upon which to categorize your emails when you publish email sending events to Amazon CloudWatch.
     */
    DimensionConfigurations: CloudWatchDimensionConfigurations;
  }
  export interface CloudWatchDimensionConfiguration {
    /**
     * The name of an Amazon CloudWatch dimension associated with an email sending metric. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), dashes (-), or colons (:).   Contain 256 characters or fewer.  
     */
    DimensionName: DimensionName;
    /**
     * The place where Amazon SES finds the value of a dimension to publish to Amazon CloudWatch. To use the message tags that you specify using an X-SES-MESSAGE-TAGS header or a parameter to the SendEmail/SendRawEmail API, specify messageTag. To use your own email headers, specify emailHeader. To put a custom tag on any link included in your email, specify linkTag.
     */
    DimensionValueSource: DimensionValueSource;
    /**
     * The default value of the dimension that is published to Amazon CloudWatch if you do not provide the value of the dimension when you send an email. The default value must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), dashes (-), at signs (@), or periods (.).   Contain 256 characters or fewer.  
     */
    DefaultDimensionValue: DefaultDimensionValue;
  }
  export type CloudWatchDimensionConfigurations = CloudWatchDimensionConfiguration[];
  export interface ConfigurationSet {
    /**
     * The name of the configuration set. The name must meet the following requirements:   Contain only letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Contain 64 characters or fewer.  
     */
    Name: ConfigurationSetName;
  }
  export type ConfigurationSetAttribute = "eventDestinations"|"trackingOptions"|"deliveryOptions"|"reputationOptions"|string;
  export type ConfigurationSetAttributeList = ConfigurationSetAttribute[];
  export type ConfigurationSetName = string;
  export type ConfigurationSets = ConfigurationSet[];
  export interface Content {
    /**
     * The textual data of the content.
     */
    Data: MessageData;
    /**
     * The character set of the content.
     */
    Charset?: Charset;
  }
  export type Counter = number;
  export interface CreateConfigurationSetEventDestinationRequest {
    /**
     * The name of the configuration set that the event destination should be associated with.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * An object that describes the Amazon Web Services service that email sending event where information is published.
     */
    EventDestination: EventDestination;
  }
  export interface CreateConfigurationSetEventDestinationResponse {
  }
  export interface CreateConfigurationSetRequest {
    /**
     * A data structure that contains the name of the configuration set.
     */
    ConfigurationSet: ConfigurationSet;
  }
  export interface CreateConfigurationSetResponse {
  }
  export interface CreateConfigurationSetTrackingOptionsRequest {
    /**
     * The name of the configuration set that the tracking options should be associated with.
     */
    ConfigurationSetName: ConfigurationSetName;
    TrackingOptions: TrackingOptions;
  }
  export interface CreateConfigurationSetTrackingOptionsResponse {
  }
  export interface CreateCustomVerificationEmailTemplateRequest {
    /**
     * The name of the custom verification email template.
     */
    TemplateName: TemplateName;
    /**
     * The email address that the custom verification email is sent from.
     */
    FromEmailAddress: FromAddress;
    /**
     * The subject line of the custom verification email.
     */
    TemplateSubject: Subject;
    /**
     * The content of the custom verification email. The total size of the email must be less than 10 MB. The message body may contain HTML, with some limitations. For more information, see Custom Verification Email Frequently Asked Questions in the Amazon SES Developer Guide.
     */
    TemplateContent: TemplateContent;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is successfully verified.
     */
    SuccessRedirectionURL: SuccessRedirectionURL;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.
     */
    FailureRedirectionURL: FailureRedirectionURL;
  }
  export interface CreateReceiptFilterRequest {
    /**
     * A data structure that describes the IP address filter to create, which consists of a name, an IP address range, and whether to allow or block mail from it.
     */
    Filter: ReceiptFilter;
  }
  export interface CreateReceiptFilterResponse {
  }
  export interface CreateReceiptRuleRequest {
    /**
     * The name of the rule set where the receipt rule is added.
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * The name of an existing rule after which the new rule is placed. If this parameter is null, the new rule is inserted at the beginning of the rule list.
     */
    After?: ReceiptRuleName;
    /**
     * A data structure that contains the specified rule's name, actions, recipients, domains, enabled status, scan status, and TLS policy.
     */
    Rule: ReceiptRule;
  }
  export interface CreateReceiptRuleResponse {
  }
  export interface CreateReceiptRuleSetRequest {
    /**
     * The name of the rule set to create. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Start and end with a letter or number.   Contain 64 characters or fewer.  
     */
    RuleSetName: ReceiptRuleSetName;
  }
  export interface CreateReceiptRuleSetResponse {
  }
  export interface CreateTemplateRequest {
    /**
     * The content of the email, composed of a subject line and either an HTML part or a text-only part.
     */
    Template: Template;
  }
  export interface CreateTemplateResponse {
  }
  export type CustomMailFromStatus = "Pending"|"Success"|"Failed"|"TemporaryFailure"|string;
  export type CustomRedirectDomain = string;
  export interface CustomVerificationEmailTemplate {
    /**
     * The name of the custom verification email template.
     */
    TemplateName?: TemplateName;
    /**
     * The email address that the custom verification email is sent from.
     */
    FromEmailAddress?: FromAddress;
    /**
     * The subject line of the custom verification email.
     */
    TemplateSubject?: Subject;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is successfully verified.
     */
    SuccessRedirectionURL?: SuccessRedirectionURL;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.
     */
    FailureRedirectionURL?: FailureRedirectionURL;
  }
  export type CustomVerificationEmailTemplates = CustomVerificationEmailTemplate[];
  export type DefaultDimensionValue = string;
  export interface DeleteConfigurationSetEventDestinationRequest {
    /**
     * The name of the configuration set from which to delete the event destination.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * The name of the event destination to delete.
     */
    EventDestinationName: EventDestinationName;
  }
  export interface DeleteConfigurationSetEventDestinationResponse {
  }
  export interface DeleteConfigurationSetRequest {
    /**
     * The name of the configuration set to delete.
     */
    ConfigurationSetName: ConfigurationSetName;
  }
  export interface DeleteConfigurationSetResponse {
  }
  export interface DeleteConfigurationSetTrackingOptionsRequest {
    /**
     * The name of the configuration set.
     */
    ConfigurationSetName: ConfigurationSetName;
  }
  export interface DeleteConfigurationSetTrackingOptionsResponse {
  }
  export interface DeleteCustomVerificationEmailTemplateRequest {
    /**
     * The name of the custom verification email template to delete.
     */
    TemplateName: TemplateName;
  }
  export interface DeleteIdentityPolicyRequest {
    /**
     * The identity that is associated with the policy to delete. You can specify the identity by using its name or by using its Amazon Resource Name (ARN). Examples: <EMAIL>, example.com, arn:aws:ses:us-east-1:************:identity/example.com. To successfully call this operation, you must own the identity.
     */
    Identity: Identity;
    /**
     * The name of the policy to be deleted.
     */
    PolicyName: PolicyName;
  }
  export interface DeleteIdentityPolicyResponse {
  }
  export interface DeleteIdentityRequest {
    /**
     * The identity to be removed from the list of identities for the Amazon Web Services account.
     */
    Identity: Identity;
  }
  export interface DeleteIdentityResponse {
  }
  export interface DeleteReceiptFilterRequest {
    /**
     * The name of the IP address filter to delete.
     */
    FilterName: ReceiptFilterName;
  }
  export interface DeleteReceiptFilterResponse {
  }
  export interface DeleteReceiptRuleRequest {
    /**
     * The name of the receipt rule set that contains the receipt rule to delete.
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * The name of the receipt rule to delete.
     */
    RuleName: ReceiptRuleName;
  }
  export interface DeleteReceiptRuleResponse {
  }
  export interface DeleteReceiptRuleSetRequest {
    /**
     * The name of the receipt rule set to delete.
     */
    RuleSetName: ReceiptRuleSetName;
  }
  export interface DeleteReceiptRuleSetResponse {
  }
  export interface DeleteTemplateRequest {
    /**
     * The name of the template to be deleted.
     */
    TemplateName: TemplateName;
  }
  export interface DeleteTemplateResponse {
  }
  export interface DeleteVerifiedEmailAddressRequest {
    /**
     * An email address to be removed from the list of verified addresses.
     */
    EmailAddress: Address;
  }
  export interface DeliveryOptions {
    /**
     * Specifies whether messages that use the configuration set are required to use Transport Layer Security (TLS). If the value is Require, messages are only delivered if a TLS connection can be established. If the value is Optional, messages can be delivered in plain text if a TLS connection can't be established.
     */
    TlsPolicy?: TlsPolicy;
  }
  export interface DescribeActiveReceiptRuleSetRequest {
  }
  export interface DescribeActiveReceiptRuleSetResponse {
    /**
     * The metadata for the currently active receipt rule set. The metadata consists of the rule set name and a timestamp of when the rule set was created.
     */
    Metadata?: ReceiptRuleSetMetadata;
    /**
     * The receipt rules that belong to the active rule set.
     */
    Rules?: ReceiptRulesList;
  }
  export interface DescribeConfigurationSetRequest {
    /**
     * The name of the configuration set to describe.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * A list of configuration set attributes to return.
     */
    ConfigurationSetAttributeNames?: ConfigurationSetAttributeList;
  }
  export interface DescribeConfigurationSetResponse {
    /**
     * The configuration set object associated with the specified configuration set.
     */
    ConfigurationSet?: ConfigurationSet;
    /**
     * A list of event destinations associated with the configuration set. 
     */
    EventDestinations?: EventDestinations;
    /**
     * The name of the custom open and click tracking domain associated with the configuration set.
     */
    TrackingOptions?: TrackingOptions;
    DeliveryOptions?: DeliveryOptions;
    /**
     * An object that represents the reputation settings for the configuration set. 
     */
    ReputationOptions?: ReputationOptions;
  }
  export interface DescribeReceiptRuleRequest {
    /**
     * The name of the receipt rule set that the receipt rule belongs to.
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * The name of the receipt rule.
     */
    RuleName: ReceiptRuleName;
  }
  export interface DescribeReceiptRuleResponse {
    /**
     * A data structure that contains the specified receipt rule's name, actions, recipients, domains, enabled status, scan status, and Transport Layer Security (TLS) policy.
     */
    Rule?: ReceiptRule;
  }
  export interface DescribeReceiptRuleSetRequest {
    /**
     * The name of the receipt rule set to describe.
     */
    RuleSetName: ReceiptRuleSetName;
  }
  export interface DescribeReceiptRuleSetResponse {
    /**
     * The metadata for the receipt rule set, which consists of the rule set name and the timestamp of when the rule set was created.
     */
    Metadata?: ReceiptRuleSetMetadata;
    /**
     * A list of the receipt rules that belong to the specified receipt rule set.
     */
    Rules?: ReceiptRulesList;
  }
  export interface Destination {
    /**
     * The recipients to place on the To: line of the message.
     */
    ToAddresses?: AddressList;
    /**
     * The recipients to place on the CC: line of the message.
     */
    CcAddresses?: AddressList;
    /**
     * The recipients to place on the BCC: line of the message.
     */
    BccAddresses?: AddressList;
  }
  export type DiagnosticCode = string;
  export type DimensionName = string;
  export type DimensionValueSource = "messageTag"|"emailHeader"|"linkTag"|string;
  export type DkimAttributes = {[key: string]: IdentityDkimAttributes};
  export type Domain = string;
  export type DsnAction = "failed"|"delayed"|"delivered"|"relayed"|"expanded"|string;
  export type DsnStatus = string;
  export type Enabled = boolean;
  export type Error = string;
  export interface EventDestination {
    /**
     * The name of the event destination. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Contain 64 characters or fewer.  
     */
    Name: EventDestinationName;
    /**
     * Sets whether Amazon SES publishes events to this destination when you send an email with the associated configuration set. Set to true to enable publishing to this destination; set to false to prevent publishing to this destination. The default value is false.
     */
    Enabled?: Enabled;
    /**
     * The type of email sending events to publish to the event destination.    send - The call was successful and Amazon SES is attempting to deliver the email.    reject - Amazon SES determined that the email contained a virus and rejected it.    bounce - The recipient's mail server permanently rejected the email. This corresponds to a hard bounce.    complaint - The recipient marked the email as spam.    delivery - Amazon SES successfully delivered the email to the recipient's mail server.    open - The recipient received the email and opened it in their email client.    click - The recipient clicked one or more links in the email.    renderingFailure - Amazon SES did not send the email because of a template rendering issue.  
     */
    MatchingEventTypes: EventTypes;
    /**
     * An object that contains the delivery stream ARN and the IAM role ARN associated with an Amazon Kinesis Firehose event destination.
     */
    KinesisFirehoseDestination?: KinesisFirehoseDestination;
    /**
     * An object that contains the names, default values, and sources of the dimensions associated with an Amazon CloudWatch event destination.
     */
    CloudWatchDestination?: CloudWatchDestination;
    /**
     * An object that contains the topic ARN associated with an Amazon Simple Notification Service (Amazon SNS) event destination.
     */
    SNSDestination?: SNSDestination;
  }
  export type EventDestinationName = string;
  export type EventDestinations = EventDestination[];
  export type EventType = "send"|"reject"|"bounce"|"complaint"|"delivery"|"open"|"click"|"renderingFailure"|string;
  export type EventTypes = EventType[];
  export type Explanation = string;
  export interface ExtensionField {
    /**
     * The name of the header to add. Must be between 1 and 50 characters, inclusive, and consist of alphanumeric (a-z, A-Z, 0-9) characters and dashes only.
     */
    Name: ExtensionFieldName;
    /**
     * The value of the header to add. Must contain 2048 characters or fewer, and must not contain newline characters ("\r" or "\n").
     */
    Value: ExtensionFieldValue;
  }
  export type ExtensionFieldList = ExtensionField[];
  export type ExtensionFieldName = string;
  export type ExtensionFieldValue = string;
  export type FailureRedirectionURL = string;
  export type FromAddress = string;
  export interface GetAccountSendingEnabledResponse {
    /**
     * Describes whether email sending is enabled or disabled for your Amazon SES account in the current Amazon Web Services Region.
     */
    Enabled?: Enabled;
  }
  export interface GetCustomVerificationEmailTemplateRequest {
    /**
     * The name of the custom verification email template to retrieve.
     */
    TemplateName: TemplateName;
  }
  export interface GetCustomVerificationEmailTemplateResponse {
    /**
     * The name of the custom verification email template.
     */
    TemplateName?: TemplateName;
    /**
     * The email address that the custom verification email is sent from.
     */
    FromEmailAddress?: FromAddress;
    /**
     * The subject line of the custom verification email.
     */
    TemplateSubject?: Subject;
    /**
     * The content of the custom verification email.
     */
    TemplateContent?: TemplateContent;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is successfully verified.
     */
    SuccessRedirectionURL?: SuccessRedirectionURL;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.
     */
    FailureRedirectionURL?: FailureRedirectionURL;
  }
  export interface GetIdentityDkimAttributesRequest {
    /**
     * A list of one or more verified identities - email addresses, domains, or both.
     */
    Identities: IdentityList;
  }
  export interface GetIdentityDkimAttributesResponse {
    /**
     * The DKIM attributes for an email address or a domain.
     */
    DkimAttributes: DkimAttributes;
  }
  export interface GetIdentityMailFromDomainAttributesRequest {
    /**
     * A list of one or more identities.
     */
    Identities: IdentityList;
  }
  export interface GetIdentityMailFromDomainAttributesResponse {
    /**
     * A map of identities to custom MAIL FROM attributes.
     */
    MailFromDomainAttributes: MailFromDomainAttributes;
  }
  export interface GetIdentityNotificationAttributesRequest {
    /**
     * A list of one or more identities. You can specify an identity by using its name or by using its Amazon Resource Name (ARN). Examples: <EMAIL>, example.com, arn:aws:ses:us-east-1:************:identity/example.com.
     */
    Identities: IdentityList;
  }
  export interface GetIdentityNotificationAttributesResponse {
    /**
     * A map of Identity to IdentityNotificationAttributes.
     */
    NotificationAttributes: NotificationAttributes;
  }
  export interface GetIdentityPoliciesRequest {
    /**
     * The identity for which the policies are retrieved. You can specify an identity by using its name or by using its Amazon Resource Name (ARN). Examples: <EMAIL>, example.com, arn:aws:ses:us-east-1:************:identity/example.com. To successfully call this operation, you must own the identity.
     */
    Identity: Identity;
    /**
     * A list of the names of policies to be retrieved. You can retrieve a maximum of 20 policies at a time. If you do not know the names of the policies that are attached to the identity, you can use ListIdentityPolicies.
     */
    PolicyNames: PolicyNameList;
  }
  export interface GetIdentityPoliciesResponse {
    /**
     * A map of policy names to policies.
     */
    Policies: PolicyMap;
  }
  export interface GetIdentityVerificationAttributesRequest {
    /**
     * A list of identities.
     */
    Identities: IdentityList;
  }
  export interface GetIdentityVerificationAttributesResponse {
    /**
     * A map of Identities to IdentityVerificationAttributes objects.
     */
    VerificationAttributes: VerificationAttributes;
  }
  export interface GetSendQuotaResponse {
    /**
     * The maximum number of emails the user is allowed to send in a 24-hour interval. A value of -1 signifies an unlimited quota.
     */
    Max24HourSend?: Max24HourSend;
    /**
     * The maximum number of emails that Amazon SES can accept from the user's account per second.  The rate at which Amazon SES accepts the user's messages might be less than the maximum send rate. 
     */
    MaxSendRate?: MaxSendRate;
    /**
     * The number of emails sent during the previous 24 hours.
     */
    SentLast24Hours?: SentLast24Hours;
  }
  export interface GetSendStatisticsResponse {
    /**
     * A list of data points, each of which represents 15 minutes of activity.
     */
    SendDataPoints?: SendDataPointList;
  }
  export interface GetTemplateRequest {
    /**
     * The name of the template to retrieve.
     */
    TemplateName: TemplateName;
  }
  export interface GetTemplateResponse {
    Template?: Template;
  }
  export type HeaderName = string;
  export type HeaderValue = string;
  export type HtmlPart = string;
  export type IAMRoleARN = string;
  export type Identity = string;
  export interface IdentityDkimAttributes {
    /**
     * Is true if DKIM signing is enabled for email sent from the identity. It's false otherwise. The default value is true.
     */
    DkimEnabled: Enabled;
    /**
     * Describes whether Amazon SES has successfully verified the DKIM DNS records (tokens) published in the domain name's DNS. (This only applies to domain identities, not email address identities.)
     */
    DkimVerificationStatus: VerificationStatus;
    /**
     * A set of character strings that represent the domain's identity. Using these tokens, you need to create DNS CNAME records that point to DKIM public keys that are hosted by Amazon SES. Amazon Web Services eventually detects that you've updated your DNS records. This detection process might take up to 72 hours. After successful detection, Amazon SES is able to DKIM-sign email originating from that domain. (This only applies to domain identities, not email address identities.) For more information about creating DNS records using DKIM tokens, see the Amazon SES Developer Guide.
     */
    DkimTokens?: VerificationTokenList;
  }
  export type IdentityList = Identity[];
  export interface IdentityMailFromDomainAttributes {
    /**
     * The custom MAIL FROM domain that the identity is configured to use.
     */
    MailFromDomain: MailFromDomainName;
    /**
     * The state that indicates whether Amazon SES has successfully read the MX record required for custom MAIL FROM domain setup. If the state is Success, Amazon SES uses the specified custom MAIL FROM domain when the verified identity sends an email. All other states indicate that Amazon SES takes the action described by BehaviorOnMXFailure.
     */
    MailFromDomainStatus: CustomMailFromStatus;
    /**
     * The action that Amazon SES takes if it cannot successfully read the required MX record when you send an email. A value of UseDefaultValue indicates that if Amazon SES cannot read the required MX record, it uses amazonses.com (or a subdomain of that) as the MAIL FROM domain. A value of RejectMessage indicates that if Amazon SES cannot read the required MX record, Amazon SES returns a MailFromDomainNotVerified error and does not send the email. The custom MAIL FROM setup states that result in this behavior are Pending, Failed, and TemporaryFailure.
     */
    BehaviorOnMXFailure: BehaviorOnMXFailure;
  }
  export interface IdentityNotificationAttributes {
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic where Amazon SES publishes bounce notifications.
     */
    BounceTopic: NotificationTopic;
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic where Amazon SES publishes complaint notifications.
     */
    ComplaintTopic: NotificationTopic;
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic where Amazon SES publishes delivery notifications.
     */
    DeliveryTopic: NotificationTopic;
    /**
     * Describes whether Amazon SES forwards bounce and complaint notifications as email. true indicates that Amazon SES forwards bounce and complaint notifications as email, while false indicates that bounce and complaint notifications are published only to the specified bounce and complaint Amazon SNS topics.
     */
    ForwardingEnabled: Enabled;
    /**
     * Describes whether Amazon SES includes the original email headers in Amazon SNS notifications of type Bounce. A value of true specifies that Amazon SES includes headers in bounce notifications, and a value of false specifies that Amazon SES does not include headers in bounce notifications.
     */
    HeadersInBounceNotificationsEnabled?: Enabled;
    /**
     * Describes whether Amazon SES includes the original email headers in Amazon SNS notifications of type Complaint. A value of true specifies that Amazon SES includes headers in complaint notifications, and a value of false specifies that Amazon SES does not include headers in complaint notifications.
     */
    HeadersInComplaintNotificationsEnabled?: Enabled;
    /**
     * Describes whether Amazon SES includes the original email headers in Amazon SNS notifications of type Delivery. A value of true specifies that Amazon SES includes headers in delivery notifications, and a value of false specifies that Amazon SES does not include headers in delivery notifications.
     */
    HeadersInDeliveryNotificationsEnabled?: Enabled;
  }
  export type IdentityType = "EmailAddress"|"Domain"|string;
  export interface IdentityVerificationAttributes {
    /**
     * The verification status of the identity: "Pending", "Success", "Failed", or "TemporaryFailure".
     */
    VerificationStatus: VerificationStatus;
    /**
     * The verification token for a domain identity. Null for email address identities.
     */
    VerificationToken?: VerificationToken;
  }
  export type InvocationType = "Event"|"RequestResponse"|string;
  export interface KinesisFirehoseDestination {
    /**
     * The ARN of the IAM role under which Amazon SES publishes email sending events to the Amazon Kinesis Firehose stream.
     */
    IAMRoleARN: AmazonResourceName;
    /**
     * The ARN of the Amazon Kinesis Firehose stream that email sending events should be published to.
     */
    DeliveryStreamARN: AmazonResourceName;
  }
  export interface LambdaAction {
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic to notify when the Lambda action is executed. You can find the ARN of a topic by using the ListTopics operation in Amazon SNS. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicArn?: AmazonResourceName;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Web Services Lambda function. An example of an Amazon Web Services Lambda function ARN is arn:aws:lambda:us-west-2:account-id:function:MyFunction. For more information about Amazon Web Services Lambda, see the Amazon Web Services Lambda Developer Guide.
     */
    FunctionArn: AmazonResourceName;
    /**
     * The invocation type of the Amazon Web Services Lambda function. An invocation type of RequestResponse means that the execution of the function immediately results in a response, and a value of Event means that the function is invoked asynchronously. The default value is Event. For information about Amazon Web Services Lambda invocation types, see the Amazon Web Services Lambda Developer Guide.  There is a 30-second timeout on RequestResponse invocations. You should use Event invocation in most cases. Use RequestResponse only to make a mail flow decision, such as whether to stop the receipt rule or the receipt rule set. 
     */
    InvocationType?: InvocationType;
  }
  export type LastAttemptDate = Date;
  export type LastFreshStart = Date;
  export interface ListConfigurationSetsRequest {
    /**
     * A token returned from a previous call to ListConfigurationSets to indicate the position of the configuration set in the configuration set list.
     */
    NextToken?: NextToken;
    /**
     * The number of configuration sets to return.
     */
    MaxItems?: MaxItems;
  }
  export interface ListConfigurationSetsResponse {
    /**
     * A list of configuration sets.
     */
    ConfigurationSets?: ConfigurationSets;
    /**
     * A token indicating that there are additional configuration sets available to be listed. Pass this token to successive calls of ListConfigurationSets. 
     */
    NextToken?: NextToken;
  }
  export interface ListCustomVerificationEmailTemplatesRequest {
    /**
     * An array the contains the name and creation time stamp for each template in your Amazon SES account.
     */
    NextToken?: NextToken;
    /**
     * The maximum number of custom verification email templates to return. This value must be at least 1 and less than or equal to 50. If you do not specify a value, or if you specify a value less than 1 or greater than 50, the operation returns up to 50 results.
     */
    MaxResults?: MaxResults;
  }
  export interface ListCustomVerificationEmailTemplatesResponse {
    /**
     * A list of the custom verification email templates that exist in your account.
     */
    CustomVerificationEmailTemplates?: CustomVerificationEmailTemplates;
    /**
     * A token indicating that there are additional custom verification email templates available to be listed. Pass this token to a subsequent call to ListTemplates to retrieve the next 50 custom verification email templates.
     */
    NextToken?: NextToken;
  }
  export interface ListIdentitiesRequest {
    /**
     * The type of the identities to list. Possible values are "EmailAddress" and "Domain". If this parameter is omitted, then all identities are listed.
     */
    IdentityType?: IdentityType;
    /**
     * The token to use for pagination.
     */
    NextToken?: NextToken;
    /**
     * The maximum number of identities per page. Possible values are 1-1000 inclusive.
     */
    MaxItems?: MaxItems;
  }
  export interface ListIdentitiesResponse {
    /**
     * A list of identities.
     */
    Identities: IdentityList;
    /**
     * The token used for pagination.
     */
    NextToken?: NextToken;
  }
  export interface ListIdentityPoliciesRequest {
    /**
     * The identity that is associated with the policy for which the policies are listed. You can specify an identity by using its name or by using its Amazon Resource Name (ARN). Examples: <EMAIL>, example.com, arn:aws:ses:us-east-1:************:identity/example.com. To successfully call this operation, you must own the identity.
     */
    Identity: Identity;
  }
  export interface ListIdentityPoliciesResponse {
    /**
     * A list of names of policies that apply to the specified identity.
     */
    PolicyNames: PolicyNameList;
  }
  export interface ListReceiptFiltersRequest {
  }
  export interface ListReceiptFiltersResponse {
    /**
     * A list of IP address filter data structures, which each consist of a name, an IP address range, and whether to allow or block mail from it.
     */
    Filters?: ReceiptFilterList;
  }
  export interface ListReceiptRuleSetsRequest {
    /**
     * A token returned from a previous call to ListReceiptRuleSets to indicate the position in the receipt rule set list.
     */
    NextToken?: NextToken;
  }
  export interface ListReceiptRuleSetsResponse {
    /**
     * The metadata for the currently active receipt rule set. The metadata consists of the rule set name and the timestamp of when the rule set was created.
     */
    RuleSets?: ReceiptRuleSetsLists;
    /**
     * A token indicating that there are additional receipt rule sets available to be listed. Pass this token to successive calls of ListReceiptRuleSets to retrieve up to 100 receipt rule sets at a time.
     */
    NextToken?: NextToken;
  }
  export interface ListTemplatesRequest {
    /**
     * A token returned from a previous call to ListTemplates to indicate the position in the list of email templates.
     */
    NextToken?: NextToken;
    /**
     * The maximum number of templates to return. This value must be at least 1 and less than or equal to 100. If more than 100 items are requested, the page size will automatically set to 100. If you do not specify a value, 10 is the default page size. 
     */
    MaxItems?: MaxItems;
  }
  export interface ListTemplatesResponse {
    /**
     * An array the contains the name and creation time stamp for each template in your Amazon SES account.
     */
    TemplatesMetadata?: TemplateMetadataList;
    /**
     * A token indicating that there are additional email templates available to be listed. Pass this token to a subsequent call to ListTemplates to retrieve the next set of email templates within your page size.
     */
    NextToken?: NextToken;
  }
  export interface ListVerifiedEmailAddressesResponse {
    /**
     * A list of email addresses that have been verified.
     */
    VerifiedEmailAddresses?: AddressList;
  }
  export type MailFromDomainAttributes = {[key: string]: IdentityMailFromDomainAttributes};
  export type MailFromDomainName = string;
  export type Max24HourSend = number;
  export type MaxItems = number;
  export type MaxResults = number;
  export type MaxSendRate = number;
  export interface Message {
    /**
     * The subject of the message: A short summary of the content, which appears in the recipient's inbox.
     */
    Subject: Content;
    /**
     * The message body.
     */
    Body: Body;
  }
  export type MessageData = string;
  export interface MessageDsn {
    /**
     * The reporting MTA that attempted to deliver the message, formatted as specified in RFC 3464 (mta-name-type; mta-name). The default value is dns; inbound-smtp.[region].amazonaws.com.
     */
    ReportingMta: ReportingMta;
    /**
     * When the message was received by the reporting mail transfer agent (MTA), in RFC 822 date-time format.
     */
    ArrivalDate?: ArrivalDate;
    /**
     * Additional X-headers to include in the DSN.
     */
    ExtensionFields?: ExtensionFieldList;
  }
  export type MessageId = string;
  export interface MessageTag {
    /**
     * The name of the tag. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Contain 256 characters or fewer.  
     */
    Name: MessageTagName;
    /**
     * The value of the tag. The value must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Contain 256 characters or fewer.  
     */
    Value: MessageTagValue;
  }
  export type MessageTagList = MessageTag[];
  export type MessageTagName = string;
  export type MessageTagValue = string;
  export type NextToken = string;
  export type NotificationAttributes = {[key: string]: IdentityNotificationAttributes};
  export type NotificationTopic = string;
  export type NotificationType = "Bounce"|"Complaint"|"Delivery"|string;
  export type Policy = string;
  export type PolicyMap = {[key: string]: Policy};
  export type PolicyName = string;
  export type PolicyNameList = PolicyName[];
  export interface PutConfigurationSetDeliveryOptionsRequest {
    /**
     * The name of the configuration set.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * Specifies whether messages that use the configuration set are required to use Transport Layer Security (TLS).
     */
    DeliveryOptions?: DeliveryOptions;
  }
  export interface PutConfigurationSetDeliveryOptionsResponse {
  }
  export interface PutIdentityPolicyRequest {
    /**
     * The identity to which that the policy applies. You can specify an identity by using its name or by using its Amazon Resource Name (ARN). Examples: <EMAIL>, example.com, arn:aws:ses:us-east-1:************:identity/example.com. To successfully call this operation, you must own the identity.
     */
    Identity: Identity;
    /**
     * The name of the policy. The policy name cannot exceed 64 characters and can only include alphanumeric characters, dashes, and underscores.
     */
    PolicyName: PolicyName;
    /**
     * The text of the policy in JSON format. The policy cannot exceed 4 KB. For information about the syntax of sending authorization policies, see the Amazon SES Developer Guide. 
     */
    Policy: Policy;
  }
  export interface PutIdentityPolicyResponse {
  }
  export interface RawMessage {
    /**
     * The raw data of the message. This data needs to base64-encoded if you are accessing Amazon SES directly through the HTTPS interface. If you are accessing Amazon SES using an Amazon Web Services SDK, the SDK takes care of the base 64-encoding for you. In all cases, the client must ensure that the message format complies with Internet email standards regarding email header fields, MIME types, and MIME encoding. The To:, CC:, and BCC: headers in the raw message can contain a group list. If you are using SendRawEmail with sending authorization, you can include X-headers in the raw message to specify the "Source," "From," and "Return-Path" addresses. For more information, see the documentation for SendRawEmail.   Do not include these X-headers in the DKIM signature, because they are removed by Amazon SES before sending the email.  For more information, go to the Amazon SES Developer Guide.
     */
    Data: RawMessageData;
  }
  export type RawMessageData = Buffer|Uint8Array|Blob|string;
  export interface ReceiptAction {
    /**
     * Saves the received message to an Amazon Simple Storage Service (Amazon S3) bucket and, optionally, publishes a notification to Amazon SNS.
     */
    S3Action?: S3Action;
    /**
     * Rejects the received email by returning a bounce response to the sender and, optionally, publishes a notification to Amazon Simple Notification Service (Amazon SNS).
     */
    BounceAction?: BounceAction;
    /**
     * Calls Amazon WorkMail and, optionally, publishes a notification to Amazon Amazon SNS.
     */
    WorkmailAction?: WorkmailAction;
    /**
     * Calls an Amazon Web Services Lambda function, and optionally, publishes a notification to Amazon SNS.
     */
    LambdaAction?: LambdaAction;
    /**
     * Terminates the evaluation of the receipt rule set and optionally publishes a notification to Amazon SNS.
     */
    StopAction?: StopAction;
    /**
     * Adds a header to the received email.
     */
    AddHeaderAction?: AddHeaderAction;
    /**
     * Publishes the email content within a notification to Amazon SNS.
     */
    SNSAction?: SNSAction;
  }
  export type ReceiptActionsList = ReceiptAction[];
  export interface ReceiptFilter {
    /**
     * The name of the IP address filter. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Start and end with a letter or number.   Contain 64 characters or fewer.  
     */
    Name: ReceiptFilterName;
    /**
     * A structure that provides the IP addresses to block or allow, and whether to block or allow incoming mail from them.
     */
    IpFilter: ReceiptIpFilter;
  }
  export type ReceiptFilterList = ReceiptFilter[];
  export type ReceiptFilterName = string;
  export type ReceiptFilterPolicy = "Block"|"Allow"|string;
  export interface ReceiptIpFilter {
    /**
     * Indicates whether to block or allow incoming mail from the specified IP addresses.
     */
    Policy: ReceiptFilterPolicy;
    /**
     * A single IP address or a range of IP addresses to block or allow, specified in Classless Inter-Domain Routing (CIDR) notation. An example of a single email address is ********. An example of a range of IP addresses is ********/24. For more information about CIDR notation, see RFC 2317.
     */
    Cidr: Cidr;
  }
  export interface ReceiptRule {
    /**
     * The name of the receipt rule. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), dashes (-), or periods (.).    Start and end with a letter or number.   Contain 64 characters or fewer.  
     */
    Name: ReceiptRuleName;
    /**
     * If true, the receipt rule is active. The default value is false.
     */
    Enabled?: Enabled;
    /**
     * Specifies whether Amazon SES should require that incoming email is delivered over a connection encrypted with Transport Layer Security (TLS). If this parameter is set to Require, Amazon SES bounces emails that are not received over TLS. The default is Optional.
     */
    TlsPolicy?: TlsPolicy;
    /**
     * The recipient domains and email addresses that the receipt rule applies to. If this field is not specified, this rule matches all recipients on all verified domains.
     */
    Recipients?: RecipientsList;
    /**
     * An ordered list of actions to perform on messages that match at least one of the recipient email addresses or domains specified in the receipt rule.
     */
    Actions?: ReceiptActionsList;
    /**
     * If true, then messages that this receipt rule applies to are scanned for spam and viruses. The default value is false.
     */
    ScanEnabled?: Enabled;
  }
  export type ReceiptRuleName = string;
  export type ReceiptRuleNamesList = ReceiptRuleName[];
  export interface ReceiptRuleSetMetadata {
    /**
     * The name of the receipt rule set. The name must meet the following requirements:   Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).   Start and end with a letter or number.   Contain 64 characters or fewer.  
     */
    Name?: ReceiptRuleSetName;
    /**
     * The date and time the receipt rule set was created.
     */
    CreatedTimestamp?: Timestamp;
  }
  export type ReceiptRuleSetName = string;
  export type ReceiptRuleSetsLists = ReceiptRuleSetMetadata[];
  export type ReceiptRulesList = ReceiptRule[];
  export type Recipient = string;
  export interface RecipientDsnFields {
    /**
     * The email address that the message was ultimately delivered to. This corresponds to the Final-Recipient in the DSN. If not specified, FinalRecipient is set to the Recipient specified in the BouncedRecipientInfo structure. Either FinalRecipient or the recipient in BouncedRecipientInfo must be a recipient of the original bounced message.  Do not prepend the FinalRecipient email address with rfc 822;, as described in RFC 3798. 
     */
    FinalRecipient?: Address;
    /**
     * The action performed by the reporting mail transfer agent (MTA) as a result of its attempt to deliver the message to the recipient address. This is required by RFC 3464.
     */
    Action: DsnAction;
    /**
     * The MTA to which the remote MTA attempted to deliver the message, formatted as specified in RFC 3464 (mta-name-type; mta-name). This parameter typically applies only to propagating synchronous bounces.
     */
    RemoteMta?: RemoteMta;
    /**
     * The status code that indicates what went wrong. This is required by RFC 3464.
     */
    Status: DsnStatus;
    /**
     * An extended explanation of what went wrong; this is usually an SMTP response. See RFC 3463 for the correct formatting of this parameter.
     */
    DiagnosticCode?: DiagnosticCode;
    /**
     * The time the final delivery attempt was made, in RFC 822 date-time format.
     */
    LastAttemptDate?: LastAttemptDate;
    /**
     * Additional X-headers to include in the DSN.
     */
    ExtensionFields?: ExtensionFieldList;
  }
  export type RecipientsList = Recipient[];
  export type RemoteMta = string;
  export type RenderedTemplate = string;
  export interface ReorderReceiptRuleSetRequest {
    /**
     * The name of the receipt rule set to reorder.
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * The specified receipt rule set's receipt rules, in order.
     */
    RuleNames: ReceiptRuleNamesList;
  }
  export interface ReorderReceiptRuleSetResponse {
  }
  export type ReportingMta = string;
  export interface ReputationOptions {
    /**
     * Describes whether email sending is enabled or disabled for the configuration set. If the value is true, then Amazon SES sends emails that use the configuration set. If the value is false, Amazon SES does not send emails that use the configuration set. The default value is true. You can change this setting using UpdateConfigurationSetSendingEnabled.
     */
    SendingEnabled?: Enabled;
    /**
     * Describes whether or not Amazon SES publishes reputation metrics for the configuration set, such as bounce and complaint rates, to Amazon CloudWatch. If the value is true, reputation metrics are published. If the value is false, reputation metrics are not published. The default value is false.
     */
    ReputationMetricsEnabled?: Enabled;
    /**
     * The date and time at which the reputation metrics for the configuration set were last reset. Resetting these metrics is known as a fresh start. When you disable email sending for a configuration set using UpdateConfigurationSetSendingEnabled and later re-enable it, the reputation metrics for the configuration set (but not for the entire Amazon SES account) are reset. If email sending for the configuration set has never been disabled and later re-enabled, the value of this attribute is null.
     */
    LastFreshStart?: LastFreshStart;
  }
  export interface S3Action {
    /**
     * The ARN of the Amazon SNS topic to notify when the message is saved to the Amazon S3 bucket. You can find the ARN of a topic by using the ListTopics operation in Amazon SNS. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicArn?: AmazonResourceName;
    /**
     * The name of the Amazon S3 bucket for incoming email.
     */
    BucketName: S3BucketName;
    /**
     * The key prefix of the Amazon S3 bucket. The key prefix is similar to a directory name that enables you to store similar data under the same directory in a bucket.
     */
    ObjectKeyPrefix?: S3KeyPrefix;
    /**
     * The customer managed key that Amazon SES should use to encrypt your emails before saving them to the Amazon S3 bucket. You can use the default managed key or a custom managed key that you created in Amazon Web Services KMS as follows:   To use the default managed key, provide an ARN in the form of arn:aws:kms:REGION:ACCOUNT-ID-WITHOUT-HYPHENS:alias/aws/ses. For example, if your Amazon Web Services account ID is ************ and you want to use the default managed key in the US West (Oregon) Region, the ARN of the default master key would be arn:aws:kms:us-west-2:************:alias/aws/ses. If you use the default managed key, you don't need to perform any extra steps to give Amazon SES permission to use the key.   To use a custom managed key that you created in Amazon Web Services KMS, provide the ARN of the managed key and ensure that you add a statement to your key's policy to give Amazon SES permission to use it. For more information about giving permissions, see the Amazon SES Developer Guide.   For more information about key policies, see the Amazon Web Services KMS Developer Guide. If you do not specify a managed key, Amazon SES does not encrypt your emails.  Your mail is encrypted by Amazon SES using the Amazon S3 encryption client before the mail is submitted to Amazon S3 for storage. It is not encrypted using Amazon S3 server-side encryption. This means that you must use the Amazon S3 encryption client to decrypt the email after retrieving it from Amazon S3, as the service has no access to use your Amazon Web Services KMS keys for decryption. This encryption client is currently available with the Amazon Web Services SDK for Java and Amazon Web Services SDK for Ruby only. For more information about client-side encryption using Amazon Web Services KMS managed keys, see the Amazon S3 Developer Guide. 
     */
    KmsKeyArn?: AmazonResourceName;
    /**
     *  The ARN of the IAM role to be used by Amazon Simple Email Service while writing to the Amazon S3 bucket, optionally encrypting your mail via the provided customer managed key, and publishing to the Amazon SNS topic. This role should have access to the following APIs:     s3:PutObject, kms:Encrypt and kms:GenerateDataKey for the given Amazon S3 bucket.    kms:GenerateDataKey for the given Amazon Web Services KMS customer managed key.     sns:Publish for the given Amazon SNS topic.    If an IAM role ARN is provided, the role (and only the role) is used to access all the given resources (Amazon S3 bucket, Amazon Web Services KMS customer managed key and Amazon SNS topic). Therefore, setting up individual resource access permissions is not required. 
     */
    IamRoleArn?: IAMRoleARN;
  }
  export type S3BucketName = string;
  export type S3KeyPrefix = string;
  export interface SNSAction {
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic to notify. You can find the ARN of a topic by using the ListTopics operation in Amazon SNS. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicArn: AmazonResourceName;
    /**
     * The encoding to use for the email within the Amazon SNS notification. UTF-8 is easier to use, but may not preserve all special characters when a message was encoded with a different encoding format. Base64 preserves all special characters. The default value is UTF-8.
     */
    Encoding?: SNSActionEncoding;
  }
  export type SNSActionEncoding = "UTF-8"|"Base64"|string;
  export interface SNSDestination {
    /**
     * The ARN of the Amazon SNS topic for email sending events. You can find the ARN of a topic by using the ListTopics Amazon SNS operation. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicARN: AmazonResourceName;
  }
  export interface SendBounceRequest {
    /**
     * The message ID of the message to be bounced.
     */
    OriginalMessageId: MessageId;
    /**
     * The address to use in the "From" header of the bounce message. This must be an identity that you have verified with Amazon SES.
     */
    BounceSender: Address;
    /**
     * Human-readable text for the bounce message to explain the failure. If not specified, the text is auto-generated based on the bounced recipient information.
     */
    Explanation?: Explanation;
    /**
     * Message-related DSN fields. If not specified, Amazon SES chooses the values.
     */
    MessageDsn?: MessageDsn;
    /**
     * A list of recipients of the bounced message, including the information required to create the Delivery Status Notifications (DSNs) for the recipients. You must specify at least one BouncedRecipientInfo in the list.
     */
    BouncedRecipientInfoList: BouncedRecipientInfoList;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the address in the "From" header of the bounce. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    BounceSenderArn?: AmazonResourceName;
  }
  export interface SendBounceResponse {
    /**
     * The message ID of the bounce message.
     */
    MessageId?: MessageId;
  }
  export interface SendBulkTemplatedEmailRequest {
    /**
     * The email address that is sending the email. This email address must be either individually verified with Amazon SES, or from a domain that has been verified with Amazon SES. For information about verifying identities, see the Amazon SES Developer Guide. If you are sending on behalf of another user and have been permitted to do so by a sending authorization policy, then you must also specify the SourceArn parameter. For more information about sending authorization, see the Amazon SES Developer Guide.  Amazon SES does not support the SMTPUTF8 extension, as described in RFC6531. For this reason, the email address string must be 7-bit ASCII. If you want to send to or from email addresses that contain Unicode characters in the domain part of an address, you must encode the domain using Punycode. Punycode is not permitted in the local part of the email address (the part before the @ sign) nor in the "friendly from" name. If you want to use Unicode characters in the "friendly from" name, you must encode the "friendly from" name using MIME encoded-word syntax, as described in Sending raw email using the Amazon SES API. For more information about Punycode, see RFC 3492. 
     */
    Source: Address;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to send for the email address specified in the Source parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you to <NAME_EMAIL>, then you would specify the SourceArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the Source <NAME_EMAIL>. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    SourceArn?: AmazonResourceName;
    /**
     * The reply-to email address(es) for the message. If the recipient replies to the message, each reply-to address receives the reply.
     */
    ReplyToAddresses?: AddressList;
    /**
     * The email address that bounces and complaints are forwarded to when feedback forwarding is enabled. If the message cannot be delivered to the recipient, then an error message is returned from the recipient's ISP; this message is forwarded to the email address specified by the ReturnPath parameter. The ReturnPath parameter is never overwritten. This email address must be either individually verified with Amazon SES, or from a domain that has been verified with Amazon SES. 
     */
    ReturnPath?: Address;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the ReturnPath parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the ReturnPathArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the ReturnPath <NAME_EMAIL>. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    ReturnPathArn?: AmazonResourceName;
    /**
     * The name of the configuration set to use when you send an email using SendBulkTemplatedEmail.
     */
    ConfigurationSetName?: ConfigurationSetName;
    /**
     * A list of tags, in the form of name/value pairs, to apply to an email that you send to a destination using SendBulkTemplatedEmail.
     */
    DefaultTags?: MessageTagList;
    /**
     * The template to use when sending this email.
     */
    Template: TemplateName;
    /**
     * The ARN of the template to use when sending this email.
     */
    TemplateArn?: AmazonResourceName;
    /**
     * A list of replacement values to apply to the template when replacement data is not specified in a Destination object. These values act as a default or fallback option when no other data is available. The template data is a JSON object, typically consisting of key-value pairs in which the keys correspond to replacement tags in the email template.
     */
    DefaultTemplateData: TemplateData;
    /**
     * One or more Destination objects. All of the recipients in a Destination receive the same version of the email. You can specify up to 50 Destination objects within a Destinations array.
     */
    Destinations: BulkEmailDestinationList;
  }
  export interface SendBulkTemplatedEmailResponse {
    /**
     * One object per intended recipient. Check each response object and retry any messages with a failure status. (Note that order of responses will be respective to order of destinations in the request.)Receipt rules enable you to specify which actions 
     */
    Status: BulkEmailDestinationStatusList;
  }
  export interface SendCustomVerificationEmailRequest {
    /**
     * The email address to verify.
     */
    EmailAddress: Address;
    /**
     * The name of the custom verification email template to use when sending the verification email.
     */
    TemplateName: TemplateName;
    /**
     * Name of a configuration set to use when sending the verification email.
     */
    ConfigurationSetName?: ConfigurationSetName;
  }
  export interface SendCustomVerificationEmailResponse {
    /**
     * The unique message identifier returned from the SendCustomVerificationEmail operation.
     */
    MessageId?: MessageId;
  }
  export interface SendDataPoint {
    /**
     * Time of the data point.
     */
    Timestamp?: Timestamp;
    /**
     * Number of emails that have been sent.
     */
    DeliveryAttempts?: Counter;
    /**
     * Number of emails that have bounced.
     */
    Bounces?: Counter;
    /**
     * Number of unwanted emails that were rejected by recipients.
     */
    Complaints?: Counter;
    /**
     * Number of emails rejected by Amazon SES.
     */
    Rejects?: Counter;
  }
  export type SendDataPointList = SendDataPoint[];
  export interface SendEmailRequest {
    /**
     * The email address that is sending the email. This email address must be either individually verified with Amazon SES, or from a domain that has been verified with Amazon SES. For information about verifying identities, see the Amazon SES Developer Guide. If you are sending on behalf of another user and have been permitted to do so by a sending authorization policy, then you must also specify the SourceArn parameter. For more information about sending authorization, see the Amazon SES Developer Guide.  Amazon SES does not support the SMTPUTF8 extension, as described in RFC6531. For this reason, the email address string must be 7-bit ASCII. If you want to send to or from email addresses that contain Unicode characters in the domain part of an address, you must encode the domain using Punycode. Punycode is not permitted in the local part of the email address (the part before the @ sign) nor in the "friendly from" name. If you want to use Unicode characters in the "friendly from" name, you must encode the "friendly from" name using MIME encoded-word syntax, as described in Sending raw email using the Amazon SES API. For more information about Punycode, see RFC 3492. 
     */
    Source: Address;
    /**
     * The destination for this email, composed of To:, CC:, and BCC: fields.
     */
    Destination: Destination;
    /**
     * The message to be sent.
     */
    Message: Message;
    /**
     * The reply-to email address(es) for the message. If the recipient replies to the message, each reply-to address receives the reply.
     */
    ReplyToAddresses?: AddressList;
    /**
     * The email address that bounces and complaints are forwarded to when feedback forwarding is enabled. If the message cannot be delivered to the recipient, then an error message is returned from the recipient's ISP; this message is forwarded to the email address specified by the ReturnPath parameter. The ReturnPath parameter is never overwritten. This email address must be either individually verified with Amazon SES, or from a domain that has been verified with Amazon SES. 
     */
    ReturnPath?: Address;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to send for the email address specified in the Source parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you to <NAME_EMAIL>, then you would specify the SourceArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the Source <NAME_EMAIL>. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    SourceArn?: AmazonResourceName;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the ReturnPath parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the ReturnPathArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the ReturnPath <NAME_EMAIL>. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    ReturnPathArn?: AmazonResourceName;
    /**
     * A list of tags, in the form of name/value pairs, to apply to an email that you send using SendEmail. Tags correspond to characteristics of the email that you define, so that you can publish email sending events.
     */
    Tags?: MessageTagList;
    /**
     * The name of the configuration set to use when you send an email using SendEmail.
     */
    ConfigurationSetName?: ConfigurationSetName;
  }
  export interface SendEmailResponse {
    /**
     * The unique message identifier returned from the SendEmail action. 
     */
    MessageId: MessageId;
  }
  export interface SendRawEmailRequest {
    /**
     * The identity's email address. If you do not provide a value for this parameter, you must specify a "From" address in the raw text of the message. (You can also specify both.)  Amazon SES does not support the SMTPUTF8 extension, as described inRFC6531. For this reason, the email address string must be 7-bit ASCII. If you want to send to or from email addresses that contain Unicode characters in the domain part of an address, you must encode the domain using Punycode. Punycode is not permitted in the local part of the email address (the part before the @ sign) nor in the "friendly from" name. If you want to use Unicode characters in the "friendly from" name, you must encode the "friendly from" name using MIME encoded-word syntax, as described in Sending raw email using the Amazon SES API. For more information about Punycode, see RFC 3492.  If you specify the Source parameter and have feedback forwarding enabled, then bounces and complaints are sent to this email address. This takes precedence over any Return-Path header that you might include in the raw text of the message.
     */
    Source?: Address;
    /**
     * A list of destinations for the message, consisting of To:, CC:, and BCC: addresses.
     */
    Destinations?: AddressList;
    /**
     * The raw email message itself. The message has to meet the following criteria:   The message has to contain a header and a body, separated by a blank line.   All of the required header fields must be present in the message.   Each part of a multipart MIME message must be formatted properly.   Attachments must be of a content type that Amazon SES supports. For a list on unsupported content types, see Unsupported Attachment Types in the Amazon SES Developer Guide.   The entire message must be base64-encoded.   If any of the MIME parts in your message contain content that is outside of the 7-bit ASCII character range, we highly recommend that you encode that content. For more information, see Sending Raw Email in the Amazon SES Developer Guide.   Per RFC 5321, the maximum length of each line of text, including the &lt;CRLF&gt;, must not exceed 1,000 characters.  
     */
    RawMessage: RawMessage;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to specify a particular "From" address in the header of the raw email. Instead of using this parameter, you can use the X-header X-SES-FROM-ARN in the raw message of the email. If you use both the FromArn parameter and the corresponding X-header, Amazon SES uses the value of the FromArn parameter.  For information about when to use this parameter, see the description of SendRawEmail in this guide, or see the Amazon SES Developer Guide. 
     */
    FromArn?: AmazonResourceName;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to send for the email address specified in the Source parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you to <NAME_EMAIL>, then you would specify the SourceArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the Source <NAME_EMAIL>. Instead of using this parameter, you can use the X-header X-SES-SOURCE-ARN in the raw message of the email. If you use both the SourceArn parameter and the corresponding X-header, Amazon SES uses the value of the SourceArn parameter.  For information about when to use this parameter, see the description of SendRawEmail in this guide, or see the Amazon SES Developer Guide. 
     */
    SourceArn?: AmazonResourceName;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the ReturnPath parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the ReturnPathArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the ReturnPath <NAME_EMAIL>. Instead of using this parameter, you can use the X-header X-SES-RETURN-PATH-ARN in the raw message of the email. If you use both the ReturnPathArn parameter and the corresponding X-header, Amazon SES uses the value of the ReturnPathArn parameter.  For information about when to use this parameter, see the description of SendRawEmail in this guide, or see the Amazon SES Developer Guide. 
     */
    ReturnPathArn?: AmazonResourceName;
    /**
     * A list of tags, in the form of name/value pairs, to apply to an email that you send using SendRawEmail. Tags correspond to characteristics of the email that you define, so that you can publish email sending events.
     */
    Tags?: MessageTagList;
    /**
     * The name of the configuration set to use when you send an email using SendRawEmail.
     */
    ConfigurationSetName?: ConfigurationSetName;
  }
  export interface SendRawEmailResponse {
    /**
     * The unique message identifier returned from the SendRawEmail action. 
     */
    MessageId: MessageId;
  }
  export interface SendTemplatedEmailRequest {
    /**
     * The email address that is sending the email. This email address must be either individually verified with Amazon SES, or from a domain that has been verified with Amazon SES. For information about verifying identities, see the Amazon SES Developer Guide. If you are sending on behalf of another user and have been permitted to do so by a sending authorization policy, then you must also specify the SourceArn parameter. For more information about sending authorization, see the Amazon SES Developer Guide.  Amazon SES does not support the SMTPUTF8 extension, as described in RFC6531. for this reason, The email address string must be 7-bit ASCII. If you want to send to or from email addresses that contain Unicode characters in the domain part of an address, you must encode the domain using Punycode. Punycode is not permitted in the local part of the email address (the part before the @ sign) nor in the "friendly from" name. If you want to use Unicode characters in the "friendly from" name, you must encode the "friendly from" name using MIME encoded-word syntax, as described in Sending raw email using the Amazon SES API. For more information about Punycode, see RFC 3492. 
     */
    Source: Address;
    /**
     * The destination for this email, composed of To:, CC:, and BCC: fields. A Destination can include up to 50 recipients across these three fields.
     */
    Destination: Destination;
    /**
     * The reply-to email address(es) for the message. If the recipient replies to the message, each reply-to address receives the reply.
     */
    ReplyToAddresses?: AddressList;
    /**
     * The email address that bounces and complaints are forwarded to when feedback forwarding is enabled. If the message cannot be delivered to the recipient, then an error message is returned from the recipient's ISP; this message is forwarded to the email address specified by the ReturnPath parameter. The ReturnPath parameter is never overwritten. This email address must be either individually verified with Amazon SES, or from a domain that has been verified with Amazon SES. 
     */
    ReturnPath?: Address;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to send for the email address specified in the Source parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you to <NAME_EMAIL>, then you would specify the SourceArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the Source <NAME_EMAIL>. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    SourceArn?: AmazonResourceName;
    /**
     * This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the ReturnPath parameter. For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:************:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the ReturnPathArn to be arn:aws:ses:us-east-1:************:identity/example.com, and the ReturnPath <NAME_EMAIL>. For more information about sending authorization, see the Amazon SES Developer Guide.
     */
    ReturnPathArn?: AmazonResourceName;
    /**
     * A list of tags, in the form of name/value pairs, to apply to an email that you send using SendTemplatedEmail. Tags correspond to characteristics of the email that you define, so that you can publish email sending events.
     */
    Tags?: MessageTagList;
    /**
     * The name of the configuration set to use when you send an email using SendTemplatedEmail.
     */
    ConfigurationSetName?: ConfigurationSetName;
    /**
     * The template to use when sending this email.
     */
    Template: TemplateName;
    /**
     * The ARN of the template to use when sending this email.
     */
    TemplateArn?: AmazonResourceName;
    /**
     * A list of replacement values to apply to the template. This parameter is a JSON object, typically consisting of key-value pairs in which the keys correspond to replacement tags in the email template.
     */
    TemplateData: TemplateData;
  }
  export interface SendTemplatedEmailResponse {
    /**
     * The unique message identifier returned from the SendTemplatedEmail action. 
     */
    MessageId: MessageId;
  }
  export type SentLast24Hours = number;
  export interface SetActiveReceiptRuleSetRequest {
    /**
     * The name of the receipt rule set to make active. Setting this value to null disables all email receiving.
     */
    RuleSetName?: ReceiptRuleSetName;
  }
  export interface SetActiveReceiptRuleSetResponse {
  }
  export interface SetIdentityDkimEnabledRequest {
    /**
     * The identity for which DKIM signing should be enabled or disabled.
     */
    Identity: Identity;
    /**
     * Sets whether DKIM signing is enabled for an identity. Set to true to enable DKIM signing for this identity; false to disable it. 
     */
    DkimEnabled: Enabled;
  }
  export interface SetIdentityDkimEnabledResponse {
  }
  export interface SetIdentityFeedbackForwardingEnabledRequest {
    /**
     * The identity for which to set bounce and complaint notification forwarding. Examples: <EMAIL>, example.com.
     */
    Identity: Identity;
    /**
     * Sets whether Amazon SES forwards bounce and complaint notifications as email. true specifies that Amazon SES forwards bounce and complaint notifications as email, in addition to any Amazon SNS topic publishing otherwise specified. false specifies that Amazon SES publishes bounce and complaint notifications only through Amazon SNS. This value can only be set to false when Amazon SNS topics are set for both Bounce and Complaint notification types.
     */
    ForwardingEnabled: Enabled;
  }
  export interface SetIdentityFeedbackForwardingEnabledResponse {
  }
  export interface SetIdentityHeadersInNotificationsEnabledRequest {
    /**
     * The identity for which to enable or disable headers in notifications. Examples: <EMAIL>, example.com.
     */
    Identity: Identity;
    /**
     * The notification type for which to enable or disable headers in notifications. 
     */
    NotificationType: NotificationType;
    /**
     * Sets whether Amazon SES includes the original email headers in Amazon SNS notifications of the specified notification type. A value of true specifies that Amazon SES includes headers in notifications, and a value of false specifies that Amazon SES does not include headers in notifications. This value can only be set when NotificationType is already set to use a particular Amazon SNS topic.
     */
    Enabled: Enabled;
  }
  export interface SetIdentityHeadersInNotificationsEnabledResponse {
  }
  export interface SetIdentityMailFromDomainRequest {
    /**
     * The verified identity.
     */
    Identity: Identity;
    /**
     * The custom MAIL FROM domain for the verified identity to use. The MAIL FROM domain must 1) be a subdomain of the verified identity, 2) not be used in a "From" address if the MAIL FROM domain is the destination of email feedback forwarding (for more information, see the Amazon SES Developer Guide), and 3) not be used to receive emails. A value of null disables the custom MAIL FROM setting for the identity.
     */
    MailFromDomain?: MailFromDomainName;
    /**
     * The action for Amazon SES to take if it cannot successfully read the required MX record when you send an email. If you choose UseDefaultValue, Amazon SES uses amazonses.com (or a subdomain of that) as the MAIL FROM domain. If you choose RejectMessage, Amazon SES returns a MailFromDomainNotVerified error and not send the email. The action specified in BehaviorOnMXFailure is taken when the custom MAIL FROM domain setup is in the Pending, Failed, and TemporaryFailure states.
     */
    BehaviorOnMXFailure?: BehaviorOnMXFailure;
  }
  export interface SetIdentityMailFromDomainResponse {
  }
  export interface SetIdentityNotificationTopicRequest {
    /**
     * The identity (email address or domain) for the Amazon SNS topic.  You can only specify a verified identity for this parameter.  You can specify an identity by using its name or by using its Amazon Resource Name (ARN). The following examples are all valid identities: <EMAIL>, example.com, arn:aws:ses:us-east-1:************:identity/example.com.
     */
    Identity: Identity;
    /**
     * The type of notifications that are published to the specified Amazon SNS topic.
     */
    NotificationType: NotificationType;
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic. If the parameter is omitted from the request or a null value is passed, SnsTopic is cleared and publishing is disabled.
     */
    SnsTopic?: NotificationTopic;
  }
  export interface SetIdentityNotificationTopicResponse {
  }
  export interface SetReceiptRulePositionRequest {
    /**
     * The name of the receipt rule set that contains the receipt rule to reposition.
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * The name of the receipt rule to reposition.
     */
    RuleName: ReceiptRuleName;
    /**
     * The name of the receipt rule after which to place the specified receipt rule.
     */
    After?: ReceiptRuleName;
  }
  export interface SetReceiptRulePositionResponse {
  }
  export interface StopAction {
    /**
     * The scope of the StopAction. The only acceptable value is RuleSet.
     */
    Scope: StopScope;
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic to notify when the stop action is taken. You can find the ARN of a topic by using the ListTopics Amazon SNS operation. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicArn?: AmazonResourceName;
  }
  export type StopScope = "RuleSet"|string;
  export type Subject = string;
  export type SubjectPart = string;
  export type SuccessRedirectionURL = string;
  export interface Template {
    /**
     * The name of the template. You use this name when you send email using the SendTemplatedEmail or SendBulkTemplatedEmail operations.
     */
    TemplateName: TemplateName;
    /**
     * The subject line of the email.
     */
    SubjectPart?: SubjectPart;
    /**
     * The email body that is visible to recipients whose email clients do not display HTML content.
     */
    TextPart?: TextPart;
    /**
     * The HTML body of the email.
     */
    HtmlPart?: HtmlPart;
  }
  export type TemplateContent = string;
  export type TemplateData = string;
  export interface TemplateMetadata {
    /**
     * The name of the template.
     */
    Name?: TemplateName;
    /**
     * The time and date the template was created.
     */
    CreatedTimestamp?: Timestamp;
  }
  export type TemplateMetadataList = TemplateMetadata[];
  export type TemplateName = string;
  export interface TestRenderTemplateRequest {
    /**
     * The name of the template to render.
     */
    TemplateName: TemplateName;
    /**
     * A list of replacement values to apply to the template. This parameter is a JSON object, typically consisting of key-value pairs in which the keys correspond to replacement tags in the email template.
     */
    TemplateData: TemplateData;
  }
  export interface TestRenderTemplateResponse {
    /**
     * The complete MIME message rendered by applying the data in the TemplateData parameter to the template specified in the TemplateName parameter.
     */
    RenderedTemplate?: RenderedTemplate;
  }
  export type TextPart = string;
  export type Timestamp = Date;
  export type TlsPolicy = "Require"|"Optional"|string;
  export interface TrackingOptions {
    /**
     * The custom subdomain that is used to redirect email recipients to the Amazon SES event tracking domain.
     */
    CustomRedirectDomain?: CustomRedirectDomain;
  }
  export interface UpdateAccountSendingEnabledRequest {
    /**
     * Describes whether email sending is enabled or disabled for your Amazon SES account in the current Amazon Web Services Region.
     */
    Enabled?: Enabled;
  }
  export interface UpdateConfigurationSetEventDestinationRequest {
    /**
     * The name of the configuration set that contains the event destination.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * The event destination object.
     */
    EventDestination: EventDestination;
  }
  export interface UpdateConfigurationSetEventDestinationResponse {
  }
  export interface UpdateConfigurationSetReputationMetricsEnabledRequest {
    /**
     * The name of the configuration set to update.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * Describes whether or not Amazon SES publishes reputation metrics for the configuration set, such as bounce and complaint rates, to Amazon CloudWatch.
     */
    Enabled: Enabled;
  }
  export interface UpdateConfigurationSetSendingEnabledRequest {
    /**
     * The name of the configuration set to update.
     */
    ConfigurationSetName: ConfigurationSetName;
    /**
     * Describes whether email sending is enabled or disabled for the configuration set. 
     */
    Enabled: Enabled;
  }
  export interface UpdateConfigurationSetTrackingOptionsRequest {
    /**
     * The name of the configuration set.
     */
    ConfigurationSetName: ConfigurationSetName;
    TrackingOptions: TrackingOptions;
  }
  export interface UpdateConfigurationSetTrackingOptionsResponse {
  }
  export interface UpdateCustomVerificationEmailTemplateRequest {
    /**
     * The name of the custom verification email template to update.
     */
    TemplateName: TemplateName;
    /**
     * The email address that the custom verification email is sent from.
     */
    FromEmailAddress?: FromAddress;
    /**
     * The subject line of the custom verification email.
     */
    TemplateSubject?: Subject;
    /**
     * The content of the custom verification email. The total size of the email must be less than 10 MB. The message body may contain HTML, with some limitations. For more information, see Custom Verification Email Frequently Asked Questions in the Amazon SES Developer Guide.
     */
    TemplateContent?: TemplateContent;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is successfully verified.
     */
    SuccessRedirectionURL?: SuccessRedirectionURL;
    /**
     * The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.
     */
    FailureRedirectionURL?: FailureRedirectionURL;
  }
  export interface UpdateReceiptRuleRequest {
    /**
     * The name of the receipt rule set that the receipt rule belongs to.
     */
    RuleSetName: ReceiptRuleSetName;
    /**
     * A data structure that contains the updated receipt rule information.
     */
    Rule: ReceiptRule;
  }
  export interface UpdateReceiptRuleResponse {
  }
  export interface UpdateTemplateRequest {
    Template: Template;
  }
  export interface UpdateTemplateResponse {
  }
  export type VerificationAttributes = {[key: string]: IdentityVerificationAttributes};
  export type VerificationStatus = "Pending"|"Success"|"Failed"|"TemporaryFailure"|"NotStarted"|string;
  export type VerificationToken = string;
  export type VerificationTokenList = VerificationToken[];
  export interface VerifyDomainDkimRequest {
    /**
     * The name of the domain to be verified for Easy DKIM signing.
     */
    Domain: Domain;
  }
  export interface VerifyDomainDkimResponse {
    /**
     * A set of character strings that represent the domain's identity. If the identity is an email address, the tokens represent the domain of that address. Using these tokens, you need to create DNS CNAME records that point to DKIM public keys that are hosted by Amazon SES. Amazon Web Services eventually detects that you've updated your DNS records. This detection process might take up to 72 hours. After successful detection, Amazon SES is able to DKIM-sign email originating from that domain. (This only applies to domain identities, not email address identities.) For more information about creating DNS records using DKIM tokens, see the Amazon SES Developer Guide.
     */
    DkimTokens: VerificationTokenList;
  }
  export interface VerifyDomainIdentityRequest {
    /**
     * The domain to be verified.
     */
    Domain: Domain;
  }
  export interface VerifyDomainIdentityResponse {
    /**
     * A TXT record that you must place in the DNS settings of the domain to complete domain verification with Amazon SES. As Amazon SES searches for the TXT record, the domain's verification status is "Pending". When Amazon SES detects the record, the domain's verification status changes to "Success". If Amazon SES is unable to detect the record within 72 hours, the domain's verification status changes to "Failed." In that case, to verify the domain, you must restart the verification process from the beginning. The domain's verification status also changes to "Success" when it is DKIM verified.
     */
    VerificationToken: VerificationToken;
  }
  export interface VerifyEmailAddressRequest {
    /**
     * The email address to be verified.
     */
    EmailAddress: Address;
  }
  export interface VerifyEmailIdentityRequest {
    /**
     * The email address to be verified.
     */
    EmailAddress: Address;
  }
  export interface VerifyEmailIdentityResponse {
  }
  export interface WorkmailAction {
    /**
     * The Amazon Resource Name (ARN) of the Amazon SNS topic to notify when the WorkMail action is called. You can find the ARN of a topic by using the ListTopics operation in Amazon SNS. For more information about Amazon SNS topics, see the Amazon SNS Developer Guide.
     */
    TopicArn?: AmazonResourceName;
    /**
     * The Amazon Resource Name (ARN) of the Amazon WorkMail organization. Amazon WorkMail ARNs use the following format:  arn:aws:workmail:&lt;region&gt;:&lt;awsAccountId&gt;:organization/&lt;workmailOrganizationId&gt;  You can find the ID of your organization by using the ListOrganizations operation in Amazon WorkMail. Amazon WorkMail organization IDs begin with "m-", followed by a string of alphanumeric characters. For information about Amazon WorkMail organizations, see the Amazon WorkMail Administrator Guide.
     */
    OrganizationArn: AmazonResourceName;
  }
  /**
   * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
   */
  export type apiVersion = "2010-12-01"|"latest"|string;
  export interface ClientApiVersions {
    /**
     * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
     */
    apiVersion?: apiVersion;
  }
  export type ClientConfiguration = ServiceConfigurationOptions & ClientApiVersions;
  /**
   * Contains interfaces for use with the SES client.
   */
  export import Types = SES;
}
export = SES;
