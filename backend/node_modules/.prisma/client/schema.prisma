// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String    @id @default(uuid()) @db.Uuid
  email        String    @unique @db.VarChar(255)
  username     String    @unique @db.VarChar(50)
  passwordHash String    @map("password_hash") @db.VarChar(255)
  firstName    String?   @map("first_name") @db.VarChar(100)
  lastName     String?   @map("last_name") @db.VarChar(100)
  avatarUrl    String?   @map("avatar_url") @db.Text
  status       String    @default("offline") @db.VarChar(20)
  lastSeen     DateTime? @map("last_seen") @db.Timestamp(6)
  isVerified   Boolean   @default(false) @map("is_verified")
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt    DateTime  @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  createdRooms       ChatRoom[]           @relation("RoomCreator")
  roomMemberships    RoomMember[]
  sentMessages       Message[]            @relation("MessageSender")
  callsInitiated     CallLog[]            @relation("CallInitiator")
  callParticipations CallParticipant[]
  messageReactions   MessageReaction[]
  resetTokens        PasswordResetToken[]

  @@map("users")
}

model ChatRoom {
  id          String   @id @default(uuid()) @db.Uuid
  name        String?  @db.VarChar(255)
  description String?  @db.Text
  type        String   @db.VarChar(20) // 'direct', 'group', 'channel'
  isPrivate   Boolean  @default(false) @map("is_private")
  createdBy   String   @map("created_by") @db.Uuid
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  creator  User         @relation("RoomCreator", fields: [createdBy], references: [id])
  members  RoomMember[]
  messages Message[]
  calls    CallLog[]

  @@map("chat_rooms")
}

model Message {
  id          String   @id @default(uuid()) @db.Uuid
  roomId      String   @map("room_id") @db.Uuid
  senderId    String   @map("sender_id") @db.Uuid
  content     String?  @db.Text
  messageType String   @default("text") @map("message_type") @db.VarChar(20) // 'text', 'image', 'file', 'call'
  fileUrl     String?  @map("file_url") @db.Text
  fileName    String?  @map("file_name") @db.VarChar(255)
  fileSize    Int?     @map("file_size")
  replyTo     String?  @map("reply_to") @db.Uuid
  isEdited    Boolean  @default(false) @map("is_edited")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamp(6)

  // Relations
  room           ChatRoom             @relation(fields: [roomId], references: [id], onDelete: Cascade)
  sender         User                 @relation("MessageSender", fields: [senderId], references: [id])
  replyToMessage Message?             @relation("MessageReply", fields: [replyTo], references: [id])
  replies        Message[]            @relation("MessageReply")
  reactions      MessageReaction[]
  readReceipts   MessageReadReceipt[]
  lastReadBy     RoomMember[]         @relation("LastReadMessage")

  @@map("messages")
}

model RoomMember {
  id                String   @id @default(uuid()) @db.Uuid
  roomId            String   @map("room_id") @db.Uuid
  userId            String   @map("user_id") @db.Uuid
  role              String   @default("member") @db.VarChar(20) // 'admin', 'moderator', 'member'
  joinedAt          DateTime @default(now()) @map("joined_at") @db.Timestamp(6)
  lastReadMessageId String?  @map("last_read_message_id") @db.Uuid

  // Relations
  room            ChatRoom @relation(fields: [roomId], references: [id], onDelete: Cascade)
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  lastReadMessage Message? @relation("LastReadMessage", fields: [lastReadMessageId], references: [id])

  @@unique([roomId, userId])
  @@map("room_members")
}

model CallLog {
  id          String    @id @default(uuid()) @db.Uuid
  roomId      String    @map("room_id") @db.Uuid
  initiatorId String    @map("initiator_id") @db.Uuid
  callType    String    @map("call_type") @db.VarChar(20) // 'audio', 'video'
  status      String    @db.VarChar(20) // 'completed', 'missed', 'declined', 'ongoing'
  duration    Int? // in seconds
  startedAt   DateTime? @map("started_at") @db.Timestamp(6)
  endedAt     DateTime? @map("ended_at") @db.Timestamp(6)
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamp(6)

  // Relations
  room         ChatRoom          @relation(fields: [roomId], references: [id])
  initiator    User              @relation("CallInitiator", fields: [initiatorId], references: [id])
  participants CallParticipant[]

  @@map("call_logs")
}

model CallParticipant {
  id       String    @id @default(uuid()) @db.Uuid
  callId   String    @map("call_id") @db.Uuid
  userId   String    @map("user_id") @db.Uuid
  joinedAt DateTime  @default(now()) @map("joined_at") @db.Timestamp(6)
  leftAt   DateTime? @map("left_at") @db.Timestamp(6)
  status   String    @db.VarChar(20) // 'joined', 'left', 'declined'

  // Relations
  call CallLog @relation(fields: [callId], references: [id], onDelete: Cascade)
  user User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([callId, userId])
  @@map("call_participants")
}

model MessageReaction {
  id        String   @id @default(uuid()) @db.Uuid
  messageId String   @map("message_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  emoji     String   @db.VarChar(10)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamp(6)

  // Relations
  message Message @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId, emoji])
  @@map("message_reactions")
}

model MessageReadReceipt {
  id        String   @id @default(uuid()) @db.Uuid
  messageId String   @map("message_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  readAt    DateTime @default(now()) @map("read_at") @db.Timestamp(6)

  // Relations
  message Message @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId])
  @@map("message_read_receipts")
}

model PasswordResetToken {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @map("user_id") @db.Uuid
  token     String    @unique @db.VarChar(255)
  expiresAt DateTime  @map("expires_at") @db.Timestamp(6)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  usedAt    DateTime? @map("used_at") @db.Timestamp(6)

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}
