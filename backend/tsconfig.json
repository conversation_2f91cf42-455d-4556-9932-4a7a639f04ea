{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"], "@/config/*": ["./config/*"], "@/middleware/*": ["./middleware/*"], "@/controllers/*": ["./controllers/*"], "@/services/*": ["./services/*"], "@/routes/*": ["./routes/*"], "@/socket/*": ["./socket/*"]}}, "include": ["src/**/*", "prisma/seed.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}