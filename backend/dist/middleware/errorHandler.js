"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.errorHandler = exports.handlePrismaError = exports.createError = exports.CustomError = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
class CustomError extends Error {
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
const createError = (message, statusCode = 500) => {
    return new CustomError(message, statusCode);
};
exports.createError = createError;
const handlePrismaError = (error) => {
    switch (error.code) {
        case 'P2002':
            const field = error.meta?.target;
            const fieldName = field?.[0] || 'field';
            return new CustomError(`${fieldName} already exists`, 409);
        case 'P2025':
            return new CustomError('Record not found', 404);
        case 'P2003':
            return new CustomError('Related record not found', 400);
        case 'P2014':
            return new CustomError('Invalid ID provided', 400);
        default:
            logger_1.logger.error('Unhandled Prisma error:', error);
            return new CustomError('Database error occurred', 500);
    }
};
exports.handlePrismaError = handlePrismaError;
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let message = 'Internal server error';
    let details = undefined;
    if (error instanceof CustomError) {
        statusCode = error.statusCode;
        message = error.message;
    }
    else if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
        const prismaError = (0, exports.handlePrismaError)(error);
        statusCode = prismaError.statusCode || 500;
        message = prismaError.message;
    }
    else if (error instanceof client_1.Prisma.PrismaClientValidationError) {
        statusCode = 400;
        message = 'Invalid data provided';
    }
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        message = error.message;
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        message = 'Invalid ID format';
    }
    logger_1.logger.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
    });
    const errorResponse = {
        error: message,
        timestamp: new Date().toISOString(),
        path: req.url,
        method: req.method,
    };
    if (environment_1.isDevelopment) {
        errorResponse.stack = error.stack;
        errorResponse.details = details;
    }
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res) => {
    res.status(404).json({
        error: 'Route not found',
        path: req.url,
        method: req.method,
    });
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=errorHandler.js.map