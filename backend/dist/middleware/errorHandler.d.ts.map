{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAIxC,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,qBAAa,WAAY,SAAQ,KAAM,YAAW,QAAQ;IACxD,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;gBAEX,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,aAAa,GAAE,OAAc;CAOrF;AAED,eAAO,MAAM,WAAW,GAAI,SAAS,MAAM,EAAE,aAAY,MAAY,gBAEpE,CAAC;AAEF,eAAO,MAAM,iBAAiB,GAAI,OAAO,MAAM,CAAC,6BAA6B,KAAG,QAwB/E,CAAC;AAEF,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,GAAG,QAAQ,EACvB,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,SAkDnB,CAAC;AAEF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,SAM1D,CAAC"}