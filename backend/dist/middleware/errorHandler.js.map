{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,2CAAwC;AACxC,2CAAwC;AACxC,sDAAqD;AAOrD,MAAa,WAAY,SAAQ,KAAK;IAIpC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI;QAClF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAXD,kCAWC;AAEM,MAAM,WAAW,GAAG,CAAC,OAAe,EAAE,aAAqB,GAAG,EAAE,EAAE;IACvE,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAC9C,CAAC,CAAC;AAFW,QAAA,WAAW,eAEtB;AAEK,MAAM,iBAAiB,GAAG,CAAC,KAA2C,EAAY,EAAE;IACzF,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,OAAO;YAEV,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,MAA8B,CAAC;YACzD,MAAM,SAAS,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;YACxC,OAAO,IAAI,WAAW,CAAC,GAAG,SAAS,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAE7D,KAAK,OAAO;YAEV,OAAO,IAAI,WAAW,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAElD,KAAK,OAAO;YAEV,OAAO,IAAI,WAAW,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QAE1D,KAAK,OAAO;YAEV,OAAO,IAAI,WAAW,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;QAErD;YACE,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,IAAI,WAAW,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC;AAxBW,QAAA,iBAAiB,qBAwB5B;AAEK,MAAM,YAAY,GAAG,CAC1B,KAAuB,EACvB,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,OAAO,GAAG,uBAAuB,CAAC;IACtC,IAAI,OAAO,GAAQ,SAAS,CAAC;IAG7B,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;QACjC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC1B,CAAC;SAAM,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;QACjE,MAAM,WAAW,GAAG,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;QAC7C,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,GAAG,CAAC;QAC3C,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;IAChC,CAAC;SAAM,IAAI,KAAK,YAAY,eAAM,CAAC,2BAA2B,EAAE,CAAC;QAC/D,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC5C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC1B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACtC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,mBAAmB,CAAC;IAChC,CAAC;IAGD,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;QAC9B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACjC,CAAC,CAAC;IAGH,MAAM,aAAa,GAAQ;QACzB,KAAK,EAAE,OAAO;QACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,GAAG;QACb,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC;IAGF,IAAI,2BAAa,EAAE,CAAC;QAClB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAClC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC;AAtDW,QAAA,YAAY,gBAsDvB;AAEK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACnB,KAAK,EAAE,iBAAiB;QACxB,IAAI,EAAE,GAAG,CAAC,GAAG;QACb,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC,CAAC;AACL,CAAC,CAAC;AANW,QAAA,eAAe,mBAM1B"}