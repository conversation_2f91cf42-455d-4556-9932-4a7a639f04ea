"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateParams = exports.validateQuery = exports.validateBody = exports.formatZodError = void 0;
const zod_1 = require("zod");
const logger_1 = require("./logger");
const formatZodError = (error) => {
    return error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
    }));
};
exports.formatZodError = formatZodError;
const validateBody = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.body);
            req.body = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const validationErrors = (0, exports.formatZodError)(error);
                return res.status(400).json({
                    error: 'Validation failed',
                    details: validationErrors,
                });
            }
            logger_1.logger.error('Validation error:', error);
            return res.status(500).json({
                error: 'Internal server error',
            });
        }
    };
};
exports.validateBody = validateBody;
const validateQuery = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.query);
            req.query = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const validationErrors = (0, exports.formatZodError)(error);
                return res.status(400).json({
                    error: 'Validation failed',
                    details: validationErrors,
                });
            }
            logger_1.logger.error('Query validation error:', error);
            return res.status(500).json({
                error: 'Internal server error',
            });
        }
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.params);
            req.params = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const validationErrors = (0, exports.formatZodError)(error);
                return res.status(400).json({
                    error: 'Validation failed',
                    details: validationErrors,
                });
            }
            logger_1.logger.error('Params validation error:', error);
            return res.status(500).json({
                error: 'Internal server error',
            });
        }
    };
};
exports.validateParams = validateParams;
//# sourceMappingURL=validation.js.map