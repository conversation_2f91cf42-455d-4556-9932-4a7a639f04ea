"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractTokenFromHeader = exports.verifyRefreshToken = exports.verifyAccessToken = exports.generateTokens = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const environment_1 = require("@/config/environment");
const logger_1 = require("./logger");
const generateTokens = (payload) => {
    try {
        const accessToken = jsonwebtoken_1.default.sign({ ...payload, type: 'access' }, environment_1.env.JWT_SECRET, { expiresIn: environment_1.env.JWT_EXPIRES_IN });
        const refreshToken = jsonwebtoken_1.default.sign({ ...payload, type: 'refresh' }, environment_1.env.JWT_REFRESH_SECRET, { expiresIn: environment_1.env.JWT_REFRESH_EXPIRES_IN });
        return { accessToken, refreshToken };
    }
    catch (error) {
        logger_1.logger.error('Error generating tokens:', error);
        throw new Error('Failed to generate tokens');
    }
};
exports.generateTokens = generateTokens;
const verifyAccessToken = (token) => {
    try {
        const decoded = jsonwebtoken_1.default.verify(token, environment_1.env.JWT_SECRET);
        if (decoded.type !== 'access') {
            throw new Error('Invalid token type');
        }
        return decoded;
    }
    catch (error) {
        logger_1.logger.error('Error verifying access token:', error);
        throw new Error('Invalid or expired token');
    }
};
exports.verifyAccessToken = verifyAccessToken;
const verifyRefreshToken = (token) => {
    try {
        const decoded = jsonwebtoken_1.default.verify(token, environment_1.env.JWT_REFRESH_SECRET);
        if (decoded.type !== 'refresh') {
            throw new Error('Invalid token type');
        }
        return decoded;
    }
    catch (error) {
        logger_1.logger.error('Error verifying refresh token:', error);
        throw new Error('Invalid or expired refresh token');
    }
};
exports.verifyRefreshToken = verifyRefreshToken;
const extractTokenFromHeader = (authHeader) => {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }
    return authHeader.substring(7);
};
exports.extractTokenFromHeader = extractTokenFromHeader;
//# sourceMappingURL=jwt.js.map