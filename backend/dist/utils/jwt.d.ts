export interface JwtPayload {
    userId: string;
    email: string;
    username: string;
    type: 'access' | 'refresh';
}
export declare const generateTokens: (payload: Omit<JwtPayload, "type">) => {
    accessToken: never;
    refreshToken: never;
};
export declare const verifyAccessToken: (token: string) => JwtPayload;
export declare const verifyRefreshToken: (token: string) => JwtPayload;
export declare const extractTokenFromHeader: (authHeader?: string) => string | null;
//# sourceMappingURL=jwt.d.ts.map