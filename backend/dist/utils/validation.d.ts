import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';
export interface ValidationError {
    field: string;
    message: string;
}
export declare const formatZodError: (error: z.ZodError) => ValidationError[];
export declare const validateBody: (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>>;
export declare const validateQuery: (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>>;
export declare const validateParams: (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>>;
//# sourceMappingURL=validation.d.ts.map