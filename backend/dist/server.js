"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const database_1 = require("@/config/database");
const redis_1 = require("@/config/redis");
const startServer = async () => {
    try {
        await database_1.prisma.$connect();
        logger_1.logger.info('✅ Database connected successfully');
        await (0, redis_1.createRedisClient)();
        app_1.server.listen(environment_1.env.PORT, () => {
            logger_1.logger.info(`🚀 Server running on port ${environment_1.env.PORT}`);
            logger_1.logger.info(`📱 Environment: ${environment_1.env.NODE_ENV}`);
            logger_1.logger.info(`🌐 CORS Origin: ${environment_1.env.CORS_ORIGIN}`);
        });
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`${signal} received, shutting down gracefully...`);
            app_1.server.close(async () => {
                logger_1.logger.info('HTTP server closed');
                try {
                    await database_1.prisma.$disconnect();
                    logger_1.logger.info('Database disconnected');
                    await (0, redis_1.disconnectRedis)();
                    logger_1.logger.info('Redis disconnected');
                    process.exit(0);
                }
                catch (error) {
                    logger_1.logger.error('Error during shutdown:', error);
                    process.exit(1);
                }
            });
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
};
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
startServer();
//# sourceMappingURL=server.js.map