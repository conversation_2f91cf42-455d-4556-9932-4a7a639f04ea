"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("@/controllers/authController");
const auth_1 = require("@/middleware/auth");
const validation_1 = require("@/utils/validation");
const room_1 = require("../../../shared/validation/room");
const router = (0, express_1.Router)();
const authController = new authController_1.AuthController();
router.use(auth_1.authenticate);
router.get('/search', (0, validation_1.validateQuery)(room_1.searchSchema), authController.searchUsers);
router.get('/profile', authController.getProfile);
exports.default = router;
//# sourceMappingURL=users.js.map