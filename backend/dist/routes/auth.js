"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("@/controllers/authController");
const auth_1 = require("@/middleware/auth");
const validation_1 = require("@/utils/validation");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const auth_2 = require("../../../shared/validation/auth");
const room_1 = require("../../../shared/validation/room");
const router = (0, express_1.Router)();
const authController = new authController_1.AuthController();
router.post('/register', rateLimiter_1.authLimiter, (0, validation_1.validateBody)(auth_2.registerSchema), authController.register);
router.post('/login', rateLimiter_1.authLimiter, (0, validation_1.validateBody)(auth_2.loginSchema), authController.login);
router.post('/refresh', authController.refreshToken);
router.post('/forgot-password', rateLimiter_1.passwordResetLimiter, (0, validation_1.validateBody)(auth_2.forgotPasswordSchema), authController.forgotPassword);
router.post('/reset-password', (0, validation_1.validateBody)(auth_2.resetPasswordSchema), authController.resetPassword);
router.post('/logout', auth_1.authenticate, authController.logout);
router.get('/profile', auth_1.authenticate, authController.getProfile);
router.put('/profile', auth_1.authenticate, (0, validation_1.validateBody)(auth_2.updateProfileSchema), authController.updateProfile);
router.put('/status', auth_1.authenticate, (0, validation_1.validateBody)(auth_2.updateStatusSchema), authController.updateStatus);
router.get('/users/search', auth_1.authenticate, (0, validation_1.validateQuery)(room_1.searchSchema), authController.searchUsers);
exports.default = router;
//# sourceMappingURL=auth.js.map