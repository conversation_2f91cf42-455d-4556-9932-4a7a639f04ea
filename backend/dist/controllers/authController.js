"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const authService_1 = require("@/services/authService");
const jwt_1 = require("@/utils/jwt");
const logger_1 = require("@/utils/logger");
const authService = new authService_1.AuthService();
class AuthController {
    async register(req, res, next) {
        try {
            const data = req.body;
            const result = await authService.register(data);
            res.status(201).json({
                message: 'User registered successfully',
                ...result,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async login(req, res, next) {
        try {
            const data = req.body;
            const result = await authService.login(data);
            res.status(200).json({
                message: 'Login successful',
                ...result,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async logout(req, res, next) {
        try {
            const user = req.user;
            await authService.logout(user.id);
            res.status(200).json({
                message: 'Logout successful',
            });
        }
        catch (error) {
            next(error);
        }
    }
    async refreshToken(req, res, next) {
        try {
            const { refreshToken } = req.body;
            if (!refreshToken) {
                res.status(400).json({
                    error: 'Refresh token is required',
                });
                return;
            }
            const payload = (0, jwt_1.verifyRefreshToken)(refreshToken);
            const { generateTokens } = await Promise.resolve().then(() => __importStar(require('@/utils/jwt')));
            const { accessToken } = generateTokens({
                userId: payload.userId,
                email: payload.email,
                username: payload.username,
            });
            res.status(200).json({
                token: accessToken,
            });
        }
        catch (error) {
            logger_1.logger.error('Token refresh failed:', error);
            res.status(401).json({
                error: 'Invalid refresh token',
            });
        }
    }
    async getProfile(req, res, next) {
        try {
            const user = req.user;
            const profile = await authService.getUserProfile(user.id);
            res.status(200).json({
                user: profile,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async updateProfile(req, res, next) {
        try {
            const user = req.user;
            const data = req.body;
            const updatedUser = await authService.updateProfile(user.id, data);
            res.status(200).json({
                message: 'Profile updated successfully',
                user: updatedUser,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async updateStatus(req, res, next) {
        try {
            const user = req.user;
            const { status } = req.body;
            await authService.updateStatus(user.id, status);
            res.status(200).json({
                message: 'Status updated successfully',
            });
        }
        catch (error) {
            next(error);
        }
    }
    async searchUsers(req, res, next) {
        try {
            const user = req.user;
            const { q: query, limit = 20 } = req.query;
            if (!query || typeof query !== 'string') {
                res.status(400).json({
                    error: 'Search query is required',
                });
                return;
            }
            const users = await authService.searchUsers(query, user.id, Number(limit));
            res.status(200).json({
                users,
            });
        }
        catch (error) {
            next(error);
        }
    }
    async forgotPassword(req, res, next) {
        try {
            const { email } = req.body;
            await authService.initiatePasswordReset(email);
            res.status(200).json({
                message: 'If an account with that email exists, a password reset link has been sent.',
            });
        }
        catch (error) {
            next(error);
        }
    }
    async resetPassword(req, res, next) {
        try {
            const { token, newPassword } = req.body;
            await authService.resetPassword(token, newPassword);
            res.status(200).json({
                message: 'Password reset successfully',
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=authController.js.map