import { Server as HTTPServer } from 'http';
export declare class SocketServer {
    private io;
    private connectedUsers;
    constructor(httpServer: HTTPServer);
    private setupMiddleware;
    private setupEventHandlers;
    private joinUserRooms;
    private handleJoinRoom;
    private handleLeaveRoom;
    private handleSendMessage;
    private handleAddReaction;
    private updateUserStatus;
    emitToUser(userId: string, event: string, data: any): void;
    emitToRoom(roomId: string, event: string, data: any): void;
    getConnectedUsers(): string[];
    isUserConnected(userId: string): boolean;
}
//# sourceMappingURL=socketServer.d.ts.map