"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketServer = void 0;
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = require("@/utils/logger");
const database_1 = require("@/config/database");
const errorHandler_1 = require("@/middleware/errorHandler");
class SocketServer {
    constructor(httpServer) {
        this.connectedUsers = new Map();
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:5173",
                methods: ["GET", "POST"],
                credentials: true,
            },
        });
        this.setupMiddleware();
        this.setupEventHandlers();
    }
    setupMiddleware() {
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token;
                if (!token) {
                    throw new errorHandler_1.CustomError('Authentication token required', 401);
                }
                const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
                const user = await database_1.prisma.user.findUnique({
                    where: { id: decoded.userId },
                    select: {
                        id: true,
                        email: true,
                        username: true,
                        status: true,
                    },
                });
                if (!user) {
                    throw new errorHandler_1.CustomError('User not found', 404);
                }
                socket.userId = user.id;
                socket.user = user;
                next();
            }
            catch (error) {
                logger_1.logger.error('Socket authentication failed:', error);
                next(new Error('Authentication failed'));
            }
        });
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info('User connected via socket', {
                userId: socket.userId,
                socketId: socket.id
            });
            this.connectedUsers.set(socket.userId, socket.id);
            this.updateUserStatus(socket.userId, 'online');
            socket.join(`user:${socket.userId}`);
            this.joinUserRooms(socket);
            socket.on('join_room', async (data) => {
                try {
                    await this.handleJoinRoom(socket, data.roomId);
                }
                catch (error) {
                    logger_1.logger.error('Error joining room:', error);
                    socket.emit('error', { message: 'Failed to join room' });
                }
            });
            socket.on('leave_room', async (data) => {
                try {
                    await this.handleLeaveRoom(socket, data.roomId);
                }
                catch (error) {
                    logger_1.logger.error('Error leaving room:', error);
                    socket.emit('error', { message: 'Failed to leave room' });
                }
            });
            socket.on('send_message', async (data) => {
                try {
                    await this.handleSendMessage(socket, data);
                }
                catch (error) {
                    logger_1.logger.error('Error sending message:', error);
                    socket.emit('error', { message: 'Failed to send message' });
                }
            });
            socket.on('typing_start', (data) => {
                socket.to(`room:${data.roomId}`).emit('user_typing', {
                    userId: socket.userId,
                    username: socket.user.username,
                    roomId: data.roomId,
                });
            });
            socket.on('typing_stop', (data) => {
                socket.to(`room:${data.roomId}`).emit('user_stopped_typing', {
                    userId: socket.userId,
                    roomId: data.roomId,
                });
            });
            socket.on('add_reaction', async (data) => {
                try {
                    await this.handleAddReaction(socket, data);
                }
                catch (error) {
                    logger_1.logger.error('Error adding reaction:', error);
                    socket.emit('error', { message: 'Failed to add reaction' });
                }
            });
            socket.on('update_status', async (data) => {
                try {
                    await this.updateUserStatus(socket.userId, data.status);
                    this.io.emit('user_status_updated', {
                        userId: socket.userId,
                        status: data.status,
                    });
                }
                catch (error) {
                    logger_1.logger.error('Error updating status:', error);
                    socket.emit('error', { message: 'Failed to update status' });
                }
            });
            socket.on('call_offer', (data) => {
                const targetSocketId = this.connectedUsers.get(data.targetUserId);
                if (targetSocketId) {
                    this.io.to(targetSocketId).emit('call_offer', {
                        fromUserId: socket.userId,
                        fromUsername: socket.user.username,
                        offer: data.offer,
                        roomId: data.roomId,
                    });
                }
            });
            socket.on('call_answer', (data) => {
                const targetSocketId = this.connectedUsers.get(data.targetUserId);
                if (targetSocketId) {
                    this.io.to(targetSocketId).emit('call_answer', {
                        fromUserId: socket.userId,
                        answer: data.answer,
                    });
                }
            });
            socket.on('ice_candidate', (data) => {
                const targetSocketId = this.connectedUsers.get(data.targetUserId);
                if (targetSocketId) {
                    this.io.to(targetSocketId).emit('ice_candidate', {
                        fromUserId: socket.userId,
                        candidate: data.candidate,
                    });
                }
            });
            socket.on('call_end', (data) => {
                const targetSocketId = this.connectedUsers.get(data.targetUserId);
                if (targetSocketId) {
                    this.io.to(targetSocketId).emit('call_end', {
                        fromUserId: socket.userId,
                    });
                }
            });
            socket.on('disconnect', async () => {
                logger_1.logger.info('User disconnected', {
                    userId: socket.userId,
                    socketId: socket.id
                });
                this.connectedUsers.delete(socket.userId);
                await this.updateUserStatus(socket.userId, 'offline');
                this.io.emit('user_status_updated', {
                    userId: socket.userId,
                    status: 'offline',
                });
            });
        });
    }
    async joinUserRooms(socket) {
        try {
            const userRooms = await database_1.prisma.roomMember.findMany({
                where: { userId: socket.userId },
                include: {
                    room: {
                        select: {
                            id: true,
                            name: true,
                            type: true,
                        },
                    },
                },
            });
            for (const membership of userRooms) {
                socket.join(`room:${membership.room.id}`);
                logger_1.logger.debug('User joined room', {
                    userId: socket.userId,
                    roomId: membership.room.id,
                    roomName: membership.room.name,
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Error joining user rooms:', error);
        }
    }
    async handleJoinRoom(socket, roomId) {
        const membership = await database_1.prisma.roomMember.findFirst({
            where: {
                userId: socket.userId,
                roomId: roomId,
            },
        });
        if (!membership) {
            throw new errorHandler_1.CustomError('Access denied to room', 403);
        }
        socket.join(`room:${roomId}`);
        socket.to(`room:${roomId}`).emit('user_joined_room', {
            userId: socket.userId,
            username: socket.user.username,
            roomId: roomId,
        });
        logger_1.logger.info('User joined room', {
            userId: socket.userId,
            roomId: roomId,
        });
    }
    async handleLeaveRoom(socket, roomId) {
        socket.leave(`room:${roomId}`);
        socket.to(`room:${roomId}`).emit('user_left_room', {
            userId: socket.userId,
            username: socket.user.username,
            roomId: roomId,
        });
        logger_1.logger.info('User left room', {
            userId: socket.userId,
            roomId: roomId,
        });
    }
    async handleSendMessage(socket, data) {
        const membership = await database_1.prisma.roomMember.findFirst({
            where: {
                userId: socket.userId,
                roomId: data.roomId,
            },
        });
        if (!membership) {
            throw new errorHandler_1.CustomError('Access denied to room', 403);
        }
        const message = await database_1.prisma.message.create({
            data: {
                content: data.content,
                type: data.type || 'text',
                senderId: socket.userId,
                roomId: data.roomId,
            },
            include: {
                sender: {
                    select: {
                        id: true,
                        username: true,
                        firstName: true,
                        lastName: true,
                        avatarUrl: true,
                    },
                },
            },
        });
        this.io.to(`room:${data.roomId}`).emit('new_message', {
            id: message.id,
            content: message.content,
            type: message.type,
            senderId: message.senderId,
            sender: message.sender,
            roomId: message.roomId,
            createdAt: message.createdAt,
        });
        logger_1.logger.info('Message sent', {
            messageId: message.id,
            senderId: socket.userId,
            roomId: data.roomId,
        });
    }
    async handleAddReaction(socket, data) {
        const message = await database_1.prisma.message.findFirst({
            where: {
                id: data.messageId,
                room: {
                    members: {
                        some: {
                            userId: socket.userId,
                        },
                    },
                },
            },
        });
        if (!message) {
            throw new errorHandler_1.CustomError('Message not found or access denied', 404);
        }
        const reaction = await database_1.prisma.messageReaction.upsert({
            where: {
                messageId_userId: {
                    messageId: data.messageId,
                    userId: socket.userId,
                },
            },
            update: {
                emoji: data.emoji,
            },
            create: {
                messageId: data.messageId,
                userId: socket.userId,
                emoji: data.emoji,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        username: true,
                    },
                },
            },
        });
        this.io.to(`room:${message.roomId}`).emit('message_reaction', {
            messageId: data.messageId,
            userId: socket.userId,
            username: reaction.user.username,
            emoji: data.emoji,
        });
        logger_1.logger.info('Reaction added', {
            messageId: data.messageId,
            userId: socket.userId,
            emoji: data.emoji,
        });
    }
    async updateUserStatus(userId, status) {
        try {
            const updateData = { status };
            if (status === 'offline') {
                updateData.lastSeen = new Date();
            }
            await database_1.prisma.user.update({
                where: { id: userId },
                data: updateData,
            });
            logger_1.logger.debug('User status updated', { userId, status });
        }
        catch (error) {
            logger_1.logger.error('Error updating user status:', error);
        }
    }
    emitToUser(userId, event, data) {
        const socketId = this.connectedUsers.get(userId);
        if (socketId) {
            this.io.to(socketId).emit(event, data);
        }
    }
    emitToRoom(roomId, event, data) {
        this.io.to(`room:${roomId}`).emit(event, data);
    }
    getConnectedUsers() {
        return Array.from(this.connectedUsers.keys());
    }
    isUserConnected(userId) {
        return this.connectedUsers.has(userId);
    }
}
exports.SocketServer = SocketServer;
//# sourceMappingURL=socketServer.js.map