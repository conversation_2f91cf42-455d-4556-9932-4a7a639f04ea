{"version": 3, "file": "authService.d.ts", "sourceRoot": "", "sources": ["../../src/services/authService.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrG,OAAO,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAErE,qBAAa,WAAW;IAChB,QAAQ,CAAC,IAAI,EAAE,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAkEpD,KAAK,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC;IAuD9C,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkBrC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IA+BtE,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAmB3F,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA8B7C,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAuCtF,qBAAqB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAiCnD,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAuCvE"}