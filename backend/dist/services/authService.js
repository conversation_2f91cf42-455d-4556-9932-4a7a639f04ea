"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const database_1 = require("@/config/database");
const encryption_1 = require("@/utils/encryption");
const jwt_1 = require("@/utils/jwt");
const errorHandler_1 = require("@/middleware/errorHandler");
const logger_1 = require("@/utils/logger");
class AuthService {
    async register(data) {
        try {
            const existingUser = await database_1.prisma.user.findFirst({
                where: {
                    OR: [
                        { email: data.email },
                        { username: data.username }
                    ]
                }
            });
            if (existingUser) {
                if (existingUser.email === data.email) {
                    throw new errorHandler_1.CustomError('Email already registered', 409);
                }
                if (existingUser.username === data.username) {
                    throw new errorHandler_1.CustomError('Username already taken', 409);
                }
            }
            const passwordHash = await (0, encryption_1.hashPassword)(data.password);
            const user = await database_1.prisma.user.create({
                data: {
                    email: data.email,
                    username: data.username,
                    passwordHash,
                    firstName: data.firstName,
                    lastName: data.lastName,
                    status: 'online',
                },
                select: {
                    id: true,
                    email: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    avatarUrl: true,
                    status: true,
                    isVerified: true,
                },
            });
            const { accessToken, refreshToken } = (0, jwt_1.generateTokens)({
                userId: user.id,
                email: user.email,
                username: user.username,
            });
            logger_1.logger.info('User registered successfully', { userId: user.id, email: user.email });
            return {
                user: user,
                token: accessToken,
                refreshToken,
            };
        }
        catch (error) {
            logger_1.logger.error('Registration failed:', error);
            throw error;
        }
    }
    async login(data) {
        try {
            const user = await database_1.prisma.user.findUnique({
                where: { email: data.email },
            });
            if (!user) {
                throw new errorHandler_1.CustomError('Invalid email or password', 401);
            }
            const isPasswordValid = await (0, encryption_1.comparePassword)(data.password, user.passwordHash);
            if (!isPasswordValid) {
                throw new errorHandler_1.CustomError('Invalid email or password', 401);
            }
            await database_1.prisma.user.update({
                where: { id: user.id },
                data: {
                    status: 'online',
                    lastSeen: new Date(),
                },
            });
            const { accessToken, refreshToken } = (0, jwt_1.generateTokens)({
                userId: user.id,
                email: user.email,
                username: user.username,
            });
            logger_1.logger.info('User logged in successfully', { userId: user.id, email: user.email });
            return {
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName || undefined,
                    lastName: user.lastName || undefined,
                    avatarUrl: user.avatarUrl || undefined,
                    status: 'online',
                    isVerified: user.isVerified,
                },
                token: accessToken,
                refreshToken,
            };
        }
        catch (error) {
            logger_1.logger.error('Login failed:', error);
            throw error;
        }
    }
    async logout(userId) {
        try {
            await database_1.prisma.user.update({
                where: { id: userId },
                data: {
                    status: 'offline',
                    lastSeen: new Date(),
                },
            });
            logger_1.logger.info('User logged out successfully', { userId });
        }
        catch (error) {
            logger_1.logger.error('Logout failed:', error);
            throw error;
        }
    }
    async updateProfile(userId, data) {
        try {
            const user = await database_1.prisma.user.update({
                where: { id: userId },
                data: {
                    firstName: data.firstName,
                    lastName: data.lastName,
                },
                select: {
                    id: true,
                    email: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    avatarUrl: true,
                    status: true,
                    lastSeen: true,
                    isVerified: true,
                    createdAt: true,
                    updatedAt: true,
                },
            });
            logger_1.logger.info('User profile updated successfully', { userId });
            return user;
        }
        catch (error) {
            logger_1.logger.error('Profile update failed:', error);
            throw error;
        }
    }
    async updateStatus(userId, status) {
        try {
            const updateData = { status };
            if (status === 'offline') {
                updateData.lastSeen = new Date();
            }
            await database_1.prisma.user.update({
                where: { id: userId },
                data: updateData,
            });
            logger_1.logger.info('User status updated successfully', { userId, status });
        }
        catch (error) {
            logger_1.logger.error('Status update failed:', error);
            throw error;
        }
    }
    async getUserProfile(userId) {
        try {
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    email: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    avatarUrl: true,
                    status: true,
                    lastSeen: true,
                    isVerified: true,
                    createdAt: true,
                    updatedAt: true,
                },
            });
            if (!user) {
                throw new errorHandler_1.CustomError('User not found', 404);
            }
            return user;
        }
        catch (error) {
            logger_1.logger.error('Get user profile failed:', error);
            throw error;
        }
    }
    async searchUsers(query, currentUserId, limit = 20) {
        try {
            const users = await database_1.prisma.user.findMany({
                where: {
                    AND: [
                        { id: { not: currentUserId } },
                        {
                            OR: [
                                { username: { contains: query, mode: 'insensitive' } },
                                { firstName: { contains: query, mode: 'insensitive' } },
                                { lastName: { contains: query, mode: 'insensitive' } },
                                { email: { contains: query, mode: 'insensitive' } },
                            ],
                        },
                    ],
                },
                select: {
                    id: true,
                    email: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    avatarUrl: true,
                    status: true,
                    lastSeen: true,
                    isVerified: true,
                    createdAt: true,
                    updatedAt: true,
                },
                take: limit,
            });
            return users;
        }
        catch (error) {
            logger_1.logger.error('User search failed:', error);
            throw error;
        }
    }
    async initiatePasswordReset(email) {
        try {
            const user = await database_1.prisma.user.findUnique({
                where: { email },
            });
            if (!user) {
                logger_1.logger.info('Password reset requested for non-existent email', { email });
                return;
            }
            const token = (0, encryption_1.generateResetToken)();
            const expiresAt = new Date(Date.now() + 60 * 60 * 1000);
            await database_1.prisma.passwordResetToken.create({
                data: {
                    userId: user.id,
                    token,
                    expiresAt,
                },
            });
            logger_1.logger.info('Password reset token generated', { userId: user.id, email });
        }
        catch (error) {
            logger_1.logger.error('Password reset initiation failed:', error);
            throw error;
        }
    }
    async resetPassword(token, newPassword) {
        try {
            const resetToken = await database_1.prisma.passwordResetToken.findFirst({
                where: {
                    token,
                    expiresAt: { gt: new Date() },
                    usedAt: null,
                },
                include: {
                    user: true,
                },
            });
            if (!resetToken) {
                throw new errorHandler_1.CustomError('Invalid or expired reset token', 400);
            }
            const passwordHash = await (0, encryption_1.hashPassword)(newPassword);
            await database_1.prisma.$transaction([
                database_1.prisma.user.update({
                    where: { id: resetToken.userId },
                    data: { passwordHash },
                }),
                database_1.prisma.passwordResetToken.update({
                    where: { id: resetToken.id },
                    data: { usedAt: new Date() },
                }),
            ]);
            logger_1.logger.info('Password reset successfully', { userId: resetToken.userId });
        }
        catch (error) {
            logger_1.logger.error('Password reset failed:', error);
            throw error;
        }
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=authService.js.map