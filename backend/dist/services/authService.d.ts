import type { RegisterInput, LoginInput, UpdateProfileInput } from '../../../shared/validation/auth';
import type { User, AuthResponse } from '../../../shared/types/user';
export declare class AuthService {
    register(data: RegisterInput): Promise<AuthResponse>;
    login(data: LoginInput): Promise<AuthResponse>;
    logout(userId: string): Promise<void>;
    updateProfile(userId: string, data: UpdateProfileInput): Promise<User>;
    updateStatus(userId: string, status: 'online' | 'away' | 'busy' | 'offline'): Promise<void>;
    getUserProfile(userId: string): Promise<User>;
    searchUsers(query: string, currentUserId: string, limit?: number): Promise<User[]>;
    initiatePasswordReset(email: string): Promise<void>;
    resetPassword(token: string, newPassword: string): Promise<void>;
}
//# sourceMappingURL=authService.d.ts.map