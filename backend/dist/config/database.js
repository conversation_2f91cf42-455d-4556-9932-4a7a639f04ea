"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
const client_1 = require("@prisma/client");
const environment_1 = require("./environment");
const createPrismaClient = () => {
    return new client_1.PrismaClient({
        log: environment_1.isDevelopment ? ['query', 'error', 'warn'] : ['error'],
        datasources: {
            db: {
                url: environment_1.env.DATABASE_URL,
            },
        },
    });
};
exports.prisma = globalThis.__prisma ?? createPrismaClient();
if (environment_1.isDevelopment) {
    globalThis.__prisma = exports.prisma;
}
process.on('beforeExit', async () => {
    await exports.prisma.$disconnect();
});
process.on('SIGINT', async () => {
    await exports.prisma.$disconnect();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    await exports.prisma.$disconnect();
    process.exit(0);
});
//# sourceMappingURL=database.js.map