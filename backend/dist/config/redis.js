"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteSession = exports.getSession = exports.setSession = exports.disconnectRedis = exports.getRedisClient = exports.createRedisClient = void 0;
const redis_1 = require("redis");
const environment_1 = require("./environment");
const logger_1 = require("@/utils/logger");
let redisClient = null;
const createRedisClient = async () => {
    if (!environment_1.env.REDIS_URL) {
        logger_1.logger.warn('Redis URL not provided, skipping Redis connection');
        return null;
    }
    try {
        const client = (0, redis_1.createClient)({
            url: environment_1.env.REDIS_URL,
            socket: {
                reconnectStrategy: (retries) => Math.min(retries * 50, 500),
            },
        });
        client.on('error', (err) => {
            logger_1.logger.error('Redis Client Error:', err);
        });
        client.on('connect', () => {
            logger_1.logger.info('Redis client connected');
        });
        client.on('ready', () => {
            logger_1.logger.info('Redis client ready');
        });
        client.on('end', () => {
            logger_1.logger.info('Redis client disconnected');
        });
        await client.connect();
        redisClient = client;
        return client;
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to Redis:', error);
        return null;
    }
};
exports.createRedisClient = createRedisClient;
const getRedisClient = () => redisClient;
exports.getRedisClient = getRedisClient;
const disconnectRedis = async () => {
    if (redisClient) {
        await redisClient.disconnect();
        redisClient = null;
    }
};
exports.disconnectRedis = disconnectRedis;
const setSession = async (key, value, ttl = 3600) => {
    if (!redisClient)
        return false;
    try {
        await redisClient.setEx(key, ttl, JSON.stringify(value));
        return true;
    }
    catch (error) {
        logger_1.logger.error('Failed to set session:', error);
        return false;
    }
};
exports.setSession = setSession;
const getSession = async (key) => {
    if (!redisClient)
        return null;
    try {
        const value = await redisClient.get(key);
        return value ? JSON.parse(value) : null;
    }
    catch (error) {
        logger_1.logger.error('Failed to get session:', error);
        return null;
    }
};
exports.getSession = getSession;
const deleteSession = async (key) => {
    if (!redisClient)
        return false;
    try {
        await redisClient.del(key);
        return true;
    }
    catch (error) {
        logger_1.logger.error('Failed to delete session:', error);
        return false;
    }
};
exports.deleteSession = deleteSession;
//# sourceMappingURL=redis.js.map