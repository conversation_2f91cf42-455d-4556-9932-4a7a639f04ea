{"name": "chat-application", "version": "1.0.0", "description": "A production-ready, real-time chat application with audio/video calling capabilities", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed", "db:studio": "cd backend && npx prisma studio"}, "keywords": ["chat", "real-time", "webrtc", "socket.io", "react", "typescript", "express", "prisma"], "author": "Chat Application Team", "license": "MIT", "devDependencies": {"@types/node": "^24.0.3", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "postcss": "^8.5.6", "tailwindcss": "^4.1.10"}, "dependencies": {"@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.522.0", "react-router-dom": "^7.6.2", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}}