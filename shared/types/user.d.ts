export interface User {
    id: string;
    email: string;
    username: string;
    firstName?: string | null;
    lastName?: string | null;
    avatarUrl?: string | null;
    status: 'online' | 'away' | 'busy' | 'offline';
    lastSeen?: Date | null;
    isVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface UserProfile {
    id: string;
    email: string;
    username: string;
    firstName?: string | null;
    lastName?: string | null;
    avatarUrl?: string | null;
    status: 'online' | 'away' | 'busy' | 'offline';
    lastSeen?: Date | null;
    isVerified: boolean;
}
export interface CreateUserRequest {
    email: string;
    username: string;
    password: string;
    firstName?: string;
    lastName?: string;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface AuthResponse {
    user: UserProfile;
    token: string;
    refreshToken?: string;
}
export interface UpdateProfileRequest {
    firstName?: string;
    lastName?: string;
    avatar?: File;
}
export interface UpdateStatusRequest {
    status: 'online' | 'away' | 'busy' | 'offline';
}
//# sourceMappingURL=user.d.ts.map