"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchSchema = exports.paginationSchema = exports.addReactionSchema = exports.updateMessageSchema = exports.sendMessageSchema = exports.updateMemberRoleSchema = exports.updateRoomSchema = exports.createRoomSchema = void 0;
const zod_1 = require("zod");
exports.createRoomSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(255).optional(),
    description: zod_1.z.string().max(1000).optional(),
    type: zod_1.z.enum(['direct', 'group', 'channel']),
    isPrivate: zod_1.z.boolean().default(false),
    userIds: zod_1.z.array(zod_1.z.string().uuid()).optional(),
});
exports.updateRoomSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(255).optional(),
    description: zod_1.z.string().max(1000).optional(),
    isPrivate: zod_1.z.boolean().optional(),
});
exports.updateMemberRoleSchema = zod_1.z.object({
    role: zod_1.z.enum(['admin', 'moderator', 'member']),
});
exports.sendMessageSchema = zod_1.z.object({
    content: zod_1.z.string().min(1).max(4000).optional(),
    messageType: zod_1.z.enum(['text', 'image', 'file']).default('text'),
    replyTo: zod_1.z.string().uuid().optional(),
});
exports.updateMessageSchema = zod_1.z.object({
    content: zod_1.z.string().min(1).max(4000),
});
exports.addReactionSchema = zod_1.z.object({
    emoji: zod_1.z.string().min(1).max(10),
});
exports.paginationSchema = zod_1.z.object({
    page: zod_1.z.coerce.number().min(1).default(1),
    limit: zod_1.z.coerce.number().min(1).max(100).default(50),
});
exports.searchSchema = zod_1.z.object({
    q: zod_1.z.string().min(1).max(100),
    page: zod_1.z.coerce.number().min(1).default(1),
    limit: zod_1.z.coerce.number().min(1).max(50).default(20),
});
//# sourceMappingURL=room.js.map