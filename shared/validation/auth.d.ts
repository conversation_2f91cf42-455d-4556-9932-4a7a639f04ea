import { z } from 'zod';
export declare const registerSchema: any;
export declare const loginSchema: any;
export declare const forgotPasswordSchema: any;
export declare const resetPasswordSchema: any;
export declare const updateProfileSchema: any;
export declare const updateStatusSchema: any;
export type RegisterInput = z.infer<typeof registerSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type UpdateStatusInput = z.infer<typeof updateStatusSchema>;
//# sourceMappingURL=auth.d.ts.map