import { z } from 'zod';
export declare const createRoomSchema: any;
export declare const updateRoomSchema: any;
export declare const updateMemberRoleSchema: any;
export declare const sendMessageSchema: any;
export declare const updateMessageSchema: any;
export declare const addReactionSchema: any;
export declare const paginationSchema: any;
export declare const searchSchema: any;
export type CreateRoomInput = z.infer<typeof createRoomSchema>;
export type UpdateRoomInput = z.infer<typeof updateRoomSchema>;
export type UpdateMemberRoleInput = z.infer<typeof updateMemberRoleSchema>;
export type SendMessageInput = z.infer<typeof sendMessageSchema>;
export type UpdateMessageInput = z.infer<typeof updateMessageSchema>;
export type AddReactionInput = z.infer<typeof addReactionSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
//# sourceMappingURL=room.d.ts.map